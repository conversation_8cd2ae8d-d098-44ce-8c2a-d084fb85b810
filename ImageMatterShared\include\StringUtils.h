#pragma once

#include <string>
#include <Windows.h>

namespace StringUtils {
    // Helper function to convert wstring to string safely
    inline std::string WideToUtf8(const std::wstring& wstr) {
        if (wstr.empty()) return std::string();
        
        // Get the required buffer size
        int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr.data(), (int)wstr.size(), NULL, 0, NULL, NULL);
        
        // Allocate the string of required size
        std::string result(size_needed, 0);
        
        // Do the actual conversion
        WideCharToMultiByte(CP_UTF8, 0, wstr.data(), (int)wstr.size(), &result[0], size_needed, NULL, NULL);
        
        return result;
    }
}
