// CUDA ProRes 4444 Encoding with AI Alpha Matting - One-Phase Processing
//
// This application processes video files to add AI-generated alpha channels
// and encodes them to ProRes 4444 format using GPU acceleration.
//

#include <Windows.h>
#include <iostream>
#include <string>
#include "VideoBackgroundRemoval.h"

// Progress callback for demonstration
bool __stdcall MyProgressCallback(int currentFrame, int totalFrames, void* userData) {
    if (currentFrame % 10 == 0) {
        if (totalFrames > 0) {
            std::cout << "Progress: " << currentFrame << "/" << totalFrames
                     << " (" << (currentFrame * 100 / totalFrames) << "%)" << std::endl;
        } else {
            std::cout << "Processed " << currentFrame << " frames" << std::endl;
        }
    }
    return true; // Continue processing
}

// Test function using the shared VideoBackgroundRemoval function
bool TestCudaProResEncoding() {
    std::wstring inputPath = L"Videos\\input short.mp4";
    std::wstring outputPath = L"Videos\\output_cuda_prores.mov";

    // Call the shared function with multi-pass processing
    int result = VideoBackgroundRemoval(
        inputPath,
        outputPath,
        ENGINE_AUTO,  // Use automatic engine selection
        MyProgressCallback,
        nullptr
    );

    return result == 0;
}

int main(int argc, char* argv[]) {

    std::cout << "=== CUDA ProRes 4444 Encoding with AI Alpha Matting (One-Phase) ===" << std::endl;

    // Test the one-phase processing
    if (TestCudaProResEncoding()) {
        std::cout << "One-phase processing completed successfully!" << std::endl;
        return 0;
    } else {
        std::cout << "One-phase processing failed!" << std::endl;
        return 1;
    }
}
