#pragma once

#include "ProcessingStep.h"
#include "FrameMetadata.h"
#include "ImageMatting.h"
#include "HeadDetector.h"
#include <memory>
#include <vector>

/**
 * Processing step for head region processing
 * Uses InsPyReNet to refine alpha matte in detected head regions for better hair detail
 * Each instance is initialized for a specific head size (model input size)
 */
class ProcessHeadRegionsStep : public ProcessingStep {
public:
    ProcessHeadRegionsStep(int headModelSize);
    virtual ~ProcessHeadRegionsStep();

    // ProcessingStep interface implementation
    bool Initialize(int width, int height, CUcontext cudaContext, cudaStream_t stream) override;
    bool Process(void* inputNv12Data, size_t inputPitch, FrameMetadata& frameMetadata) override;
    const char* GetStepName() const override;
    bool IsInitialized() const override { return m_initialized; }

    // Get the head model size this step processes
    int GetHeadModelSize() const { return m_headModelSize; }

private:
    // CUDA buffers for processing
    float* m_rgbBuffer;
    size_t m_rgbBufferSize;

    float* m_decompressedAlpha;
    float* m_trimapBuffer;
    float* m_headRgbBuffer;
    float* m_headAlphaBuffer;
    float* m_rgbPlanar;
    float* m_originalAlphaTexture;

    // Processing parameters
    int m_width;
    int m_height;
    int m_headModelSize;  // The specific head model size this step processes
    CUcontext m_cudaContext;
    cudaStream_t m_stream;
    bool m_initialized;

    // InsPyReNet instance for head processing
    std::unique_ptr<ImageMatting> m_inspyreNet;

    // Step name with model size
    mutable std::string m_stepName;

    // Head detection results
    std::vector<Box> m_headDetections;

    // Helper methods
    bool AllocateBuffers();
    void DeallocateBuffers();
    bool DecompressAlpha(const FrameMetadata& frameMetadata);
    void ProcessHeadRegionsImpl();
};
