#include "UpscaleResizingKernels.cuh"
#include <device_launch_parameters.h>
#include <Windows.h>
#include <iostream>
#include <math_constants.h>

#ifdef __CUDACC__

// CUDA kernels for high-quality image upscaling

// Bicubic interpolation kernel for smooth upscaling
__device__ inline float cubicWeight(float x) {
    x = fabsf(x);
    if (x >= 2.0f) return 0.0f;
    if (x <= 1.0f) {
        return 1.5f * x * x * x - 2.5f * x * x + 1.0f;
    }
    else {
        return -0.5f * x * x * x + 2.5f * x * x - 4.0f * x + 2.0f;
    }
}

__global__ void BicubicUpscaleKernel(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels) {
    const int x = blockIdx.x * blockDim.x + threadIdx.x;
    const int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x < outputWidth && y < outputHeight) {
        // Calculate source coordinates
        float scaleX = static_cast<float>(inputWidth) / outputWidth;
        float scaleY = static_cast<float>(inputHeight) / outputHeight;
        float srcX = (static_cast<float>(x) + 0.5f) * scaleX - 0.5f;
        float srcY = (static_cast<float>(y) + 0.5f) * scaleY - 0.5f;

        // Bicubic support is 2 pixels in each direction
        int x_start = max(0, static_cast<int>(floorf(srcX)) - 1);
        int x_end = min(inputWidth - 1, static_cast<int>(ceilf(srcX)) + 2);
        int y_start = max(0, static_cast<int>(floorf(srcY)) - 1);
        int y_end = min(inputHeight - 1, static_cast<int>(ceilf(srcY)) + 2);

        for (int c = 0; c < channels; c++) {
            float sum = 0.0f;
            float weightSum = 0.0f;

            int planeSize = inputWidth * inputHeight;
            int planeOffset = c * planeSize;

            // Apply bicubic filter
            for (int iy = y_start; iy <= y_end; iy++) {
                for (int ix = x_start; ix <= x_end; ix++) {
                    float dx = srcX - static_cast<float>(ix);
                    float dy = srcY - static_cast<float>(iy);

                    float weightX = cubicWeight(dx);
                    float weightY = cubicWeight(dy);
                    float weight = weightX * weightY;

                    if (weight != 0.0f) {
                        float pixel = input[planeOffset + (iy * inputWidth + ix)];
                        sum += pixel * weight;
                        weightSum += weight;
                    }
                }
            }

            // Normalize and clamp result
            float value = (weightSum > 0.0f) ? sum / weightSum : 0.0f;
            value = fmaxf(0.0f, fminf(1.0f, value)); // Clamp to [0,1] range

            int outPlaneSize = outputWidth * outputHeight;
            int outPlaneOffset = c * outPlaneSize;
            output[outPlaneOffset + (y * outputWidth + x)] = value;
        }
    }
}

// Edge-preserving upscaling kernel using edge-directed interpolation
__global__ void EdgeDirectedUpscaleKernel(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels) {
    const int x = blockIdx.x * blockDim.x + threadIdx.x;
    const int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x < outputWidth && y < outputHeight) {
        float scaleX = static_cast<float>(inputWidth) / outputWidth;
        float scaleY = static_cast<float>(inputHeight) / outputHeight;
        float srcX = (static_cast<float>(x) + 0.5f) * scaleX - 0.5f;
        float srcY = (static_cast<float>(y) + 0.5f) * scaleY - 0.5f;

        int x1 = static_cast<int>(floorf(srcX));
        int y1 = static_cast<int>(floorf(srcY));
        int x2 = min(x1 + 1, inputWidth - 1);
        int y2 = min(y1 + 1, inputHeight - 1);

        // Ensure bounds
        x1 = max(0, x1);
        y1 = max(0, y1);

        float xFrac = srcX - x1;
        float yFrac = srcY - y1;

        for (int c = 0; c < channels; c++) {
            int planeSize = inputWidth * inputHeight;
            int planeOffset = c * planeSize;

            // Get the four corner pixels
            float p11 = input[planeOffset + (y1 * inputWidth + x1)];
            float p12 = input[planeOffset + (y1 * inputWidth + x2)];
            float p21 = input[planeOffset + (y2 * inputWidth + x1)];
            float p22 = input[planeOffset + (y2 * inputWidth + x2)];

            // Calculate gradients to detect edges
            float gradH = fabsf(p12 - p11) + fabsf(p22 - p21); // Horizontal gradient
            float gradV = fabsf(p21 - p11) + fabsf(p22 - p12); // Vertical gradient
            float gradD1 = fabsf(p22 - p11); // Diagonal gradient
            float gradD2 = fabsf(p21 - p12); // Anti-diagonal gradient

            float value;

            // Choose interpolation direction based on gradients
            if (gradH < gradV && gradH < gradD1 && gradH < gradD2) {
                // Horizontal interpolation (less horizontal variation)
                float top = p11 * (1 - xFrac) + p12 * xFrac;
                float bottom = p21 * (1 - xFrac) + p22 * xFrac;
                value = top * (1 - yFrac) + bottom * yFrac;
            }
            else if (gradV < gradD1 && gradV < gradD2) {
                // Vertical interpolation (less vertical variation)
                float left = p11 * (1 - yFrac) + p21 * yFrac;
                float right = p12 * (1 - yFrac) + p22 * yFrac;
                value = left * (1 - xFrac) + right * xFrac;
            }
            else {
                // Standard bilinear interpolation
                float top = p11 * (1 - xFrac) + p12 * xFrac;
                float bottom = p21 * (1 - xFrac) + p22 * xFrac;
                value = top * (1 - yFrac) + bottom * yFrac;
            }

            // Apply sharpening for upscaling
            float sharpen = 0.1f; // Adjustable sharpening factor
            float center = value;
            float sharpened = center + sharpen * (center - (p11 + p12 + p21 + p22) * 0.25f);
            value = fmaxf(0.0f, fminf(1.0f, sharpened));

            int outPlaneSize = outputWidth * outputHeight;
            int outPlaneOffset = c * outPlaneSize;
            output[outPlaneOffset + (y * outputWidth + x)] = value;
        }
    }
}

// Lanczos upscaling kernel for sharp detail preservation
__device__ inline float lanczos3_upscale(float x) {
    if (x == 0.0f) return 1.0f;
    if (fabsf(x) >= 3.0f) return 0.0f;

    float pi_x = 3.14159265359f * x;
    float pi_x_div3 = pi_x / 3.0f;
    return 3.0f * sinf(pi_x) * sinf(pi_x_div3) / (pi_x * pi_x);
}

__global__ void LanczosUpscaleKernel(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels) {
    const int x = blockIdx.x * blockDim.x + threadIdx.x;
    const int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x < outputWidth && y < outputHeight) {
        float scaleX = static_cast<float>(inputWidth) / outputWidth;
        float scaleY = static_cast<float>(inputHeight) / outputHeight;
        float srcX = (static_cast<float>(x) + 0.5f) * scaleX - 0.5f;
        float srcY = (static_cast<float>(y) + 0.5f) * scaleY - 0.5f;

        // Lanczos-3 support
        int x_start = max(0, static_cast<int>(floorf(srcX)) - 2);
        int x_end = min(inputWidth - 1, static_cast<int>(ceilf(srcX)) + 2);
        int y_start = max(0, static_cast<int>(floorf(srcY)) - 2);
        int y_end = min(inputHeight - 1, static_cast<int>(ceilf(srcY)) + 2);

        for (int c = 0; c < channels; c++) {
            float sum = 0.0f;
            float weightSum = 0.0f;

            int planeSize = inputWidth * inputHeight;
            int planeOffset = c * planeSize;

            for (int iy = y_start; iy <= y_end; iy++) {
                for (int ix = x_start; ix <= x_end; ix++) {
                    float dx = srcX - static_cast<float>(ix);
                    float dy = srcY - static_cast<float>(iy);

                    float weightX = lanczos3_upscale(dx);
                    float weightY = lanczos3_upscale(dy);
                    float weight = weightX * weightY;

                    if (weight != 0.0f) {
                        float pixel = input[planeOffset + (iy * inputWidth + ix)];
                        sum += pixel * weight;
                        weightSum += weight;
                    }
                }
            }

            float value = (weightSum > 0.0f) ? sum / weightSum : 0.0f;
            value = fmaxf(0.0f, fminf(1.0f, value));

            int outPlaneSize = outputWidth * outputHeight;
            int outPlaneOffset = c * outPlaneSize;
            output[outPlaneOffset + (y * outputWidth + x)] = value;
        }
    }
}

// Launcher functions
cudaError_t LaunchBicubicUpscaleKernel(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels) {
    dim3 blockDim(16, 16);
    dim3 gridDim(
        (outputWidth + blockDim.x - 1) / blockDim.x,
        (outputHeight + blockDim.y - 1) / blockDim.y
    );
    BicubicUpscaleKernel << <gridDim, blockDim >> > (
        output, outputWidth, outputHeight,
        input, inputWidth, inputHeight, channels);
    return cudaGetLastError();
}

cudaError_t BicubicUpscaleKernelLauncher(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels, cudaStream_t stream) {
    dim3 blockDim(16, 16);
    dim3 gridDim((outputWidth + blockDim.x - 1) / blockDim.x,
        (outputHeight + blockDim.y - 1) / blockDim.y);

    BicubicUpscaleKernel << <gridDim, blockDim, 0, stream >> > (
        output, outputWidth, outputHeight,
        input, inputWidth, inputHeight, channels);

    return cudaGetLastError();
}

cudaError_t LaunchEdgeDirectedUpscaleKernel(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels) {
    dim3 blockDim(16, 16);
    dim3 gridDim(
        (outputWidth + blockDim.x - 1) / blockDim.x,
        (outputHeight + blockDim.y - 1) / blockDim.y
    );
    EdgeDirectedUpscaleKernel << <gridDim, blockDim >> > (
        output, outputWidth, outputHeight,
        input, inputWidth, inputHeight, channels);
    return cudaGetLastError();
}

cudaError_t EdgeDirectedUpscaleKernelLauncher(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels, cudaStream_t stream) {
    dim3 blockDim(16, 16);
    dim3 gridDim((outputWidth + blockDim.x - 1) / blockDim.x,
        (outputHeight + blockDim.y - 1) / blockDim.y);

    EdgeDirectedUpscaleKernel << <gridDim, blockDim, 0, stream >> > (
        output, outputWidth, outputHeight,
        input, inputWidth, inputHeight, channels);

    return cudaGetLastError();
}

cudaError_t LaunchLanczosUpscaleKernel(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels) {
    dim3 blockDim(16, 16);
    dim3 gridDim(
        (outputWidth + blockDim.x - 1) / blockDim.x,
        (outputHeight + blockDim.y - 1) / blockDim.y
    );
    LanczosUpscaleKernel << <gridDim, blockDim >> > (
        output, outputWidth, outputHeight,
        input, inputWidth, inputHeight, channels);
    return cudaGetLastError();
}

cudaError_t LanczosUpscaleKernelLauncher(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels, cudaStream_t stream) {
    dim3 blockDim(16, 16);
    dim3 gridDim((outputWidth + blockDim.x - 1) / blockDim.x,
        (outputHeight + blockDim.y - 1) / blockDim.y);

    LanczosUpscaleKernel << <gridDim, blockDim, 0, stream >> > (
        output, outputWidth, outputHeight,
        input, inputWidth, inputHeight, channels);

    return cudaGetLastError();
}

#endif