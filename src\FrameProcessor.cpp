#include "FrameProcessor.h"
#include "main_Kernels.cuh"
#include "Helpers.h"
#include "Helpers_Heads.h"
#include "ImageMattingFactory.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <mutex>
#include <algorithm>
#include <cuda.h>

// Constants
const int HeadRegionExtension = 30;
const int MaxHeadCount = 50;
const int TRIMAP_IMPROVE_MAX_DISTANCE = 55;

// Helper function to check if the region is inside a head
bool isInsideHeadRegions(int x, int yRegion, const std::vector<Box>& headBoxes, int regionSize, int videoWidth) {
    for (const auto& head : headBoxes) {
        if (head.width <= 0 || head.height <= 0) continue;

        int regionY = yRegion * regionSize;
        int regionX = x;

        // Check if region is completely inside this head box
        if (regionX >= head.x &&
            // regionY >= head.y && We don't test the starting y of the region because we assume there is nothing above the head. This removes many false positives in the head box.
            regionX + regionSize <= head.x + head.width &&
            regionY + regionSize <= head.y + head.height) {
            return true;
        }
    }
    return false;
}

FrameProcessor::FrameProcessor()
    : processingStream(nullptr)
    , bodyStream(nullptr)
    , headStream(nullptr)
    , d_regionsBufferBody(nullptr)
    , d_regionsBufferHair(nullptr)
    , d_trimapBuffer(nullptr)
    , d_edgePositions(nullptr)
    , d_headBoxes(nullptr)
    , d_alphaArray(nullptr)
    , d_originalAlphaTexture(0)
    , d_horizontalBgData(nullptr)
    , d_verticalBgData(nullptr)
    , h_regionsBufferBody(nullptr)
    , h_regionsBufferHair(nullptr)
    , regionsBufferSizeBody(0)
    , regionsBufferSizeHair(0)
    , videoWidth(0)
    , videoHeight(0)
    , regionSizeBody(0)
    , regionSizeHair(0)
    , IndexNetModelInputSize(320)
    , MaxUncertainRegionWidthForBody(5)
    , MaxUncertainRegionWidthForHair(20)
    , NumVerticalRegionCountBody(0)
    , NumVerticalRegionCountHair(0)
    , headDetectionResults(50) // MaxHeadCount
{
    // Create CUDA streams for parallel processing
    cudaStreamCreate(&processingStream);
    cudaStreamCreate(&bodyStream);
    cudaStreamCreate(&headStream);
}

FrameProcessor::~FrameProcessor() {
    Cleanup();
}

bool FrameProcessor::Initialize(int width, int height, cudaStream_t mainStream) {
    videoWidth = width;
    videoHeight = height;
    processingStream = mainStream;

    if (!FrameProcessor::InitializeInitialMatte(width, height, processingStream)) {
		return false;
	}

    if (!FrameProcessor::InitializeRefinement(width, height, processingStream)) {
		return false;
	}

    return true;
}

bool FrameProcessor::InitializeInitialMatte(int width, int height, cudaStream_t mainStream) {
    videoWidth = width;
    videoHeight = height;
    processingStream = mainStream;

    // Initialize initial image matting model using new unified interface
	 //initialImageMatter = ImageMattingFactory::Init(
	 //   ModelType::RMBG2_0, videoWidth, videoHeight, processingStream,
	 //   InferenceBackend::AUTO, ImageMattingFactory::BestModelSelectionMethod::FIXED_SIZE, false);
	initialImageMatter = ImageMattingFactory::Init(
		ModelType::INSPYRENET, videoWidth, videoHeight, processingStream,
		InferenceBackend::AUTO, ImageMattingFactory::BestModelSelectionMethod::ASPECT_RATIO_CLOSEST_FIT, false);
	 //initialImageMatter = ImageMattingFactory::Init(
	 //    ModelType::RMBG1_4, videoWidth, videoHeight, processingStream,
	 //    InferenceBackend::AUTO, ImageMattingFactory::BestModelSelectionMethod::
	 //    FIXED_SIZE, false);
	 //initialImageMatter = ImageMattingFactory::Init(
	 //    ModelType::BEN2, videoWidth, videoHeight, processingStream,
	 //    InferenceBackend::AUTO, ImageMattingFactory::BestModelSelectionMethod::
	 //    FIXED_SIZE, false);
	if (!initialImageMatter) {
		std::cerr << "Failed to initialize initial image matting model" << std::endl;
		return false;
	}

    return true;
}

bool FrameProcessor::InitializeRefinement(int width, int height, cudaStream_t mainStream) {
    videoWidth = width;
    videoHeight = height;
    processingStream = mainStream;

    // Initialize head detector
    headDetector = std::make_unique<HeadDetector>();
    if (!headDetector->Initialize(videoWidth, videoHeight, processingStream)) {
        std::cerr << "Failed to initialize head detector" << std::endl;
        return false;
    }

    // Initialize IndexNet for refinement
    indexNetImageMatter = ImageMattingFactory::Init(
        ModelType::INDEXNET, videoWidth, videoHeight, processingStream,
        InferenceBackend::AUTO, ImageMattingFactory::BestModelSelectionMethod::
        ASPECT_RATIO_CLOSEST_FIT, false);
    if (!indexNetImageMatter) {
        std::cerr << "Failed to initialize IndexNet image matting model" << std::endl;
        return false;
    }

    // Allocate all buffers
    if (!AllocateBuffers()) {
        std::cerr << "Failed to allocate buffers" << std::endl;
        return false;
    }

    return true;
}

bool FrameProcessor::AllocateBuffers() {
    // Allocate processing buffers (no need for NV12 buffers since we work directly with input/output)
    size_t alphaBufferSize = videoWidth * videoHeight * sizeof(float);
    size_t rgbBufferSize = videoWidth * videoHeight * 3 * sizeof(float);

    CUDA_CHECK(cudaMalloc(&d_trimapBuffer, alphaBufferSize));

    regionSizeBody = IndexNetModelInputSize - 2 * MaxUncertainRegionWidthForBody;
    regionSizeHair = IndexNetModelInputSize - 2 * MaxUncertainRegionWidthForHair;

    // Allocate regions buffers
    NumVerticalRegionCountBody = (videoHeight + regionSizeBody - 1) / regionSizeBody;
    NumVerticalRegionCountHair = (videoHeight + regionSizeHair - 1) / regionSizeHair;

    regionsBufferSizeBody = NumVerticalRegionCountBody * videoWidth * sizeof(unsigned char);
    regionsBufferSizeHair = NumVerticalRegionCountHair * videoWidth * sizeof(unsigned char);

    CUDA_CHECK(cudaMalloc(&d_regionsBufferBody, regionsBufferSizeBody));
    CUDA_CHECK(cudaMalloc(&d_regionsBufferHair, regionsBufferSizeHair));

    // Allocate host buffers
    h_regionsBufferBody = new unsigned char[NumVerticalRegionCountBody * videoWidth];
    h_regionsBufferHair = new unsigned char[NumVerticalRegionCountHair * videoWidth];

    // Allocate other device buffers
    CUDA_CHECK(cudaMalloc(&d_edgePositions, videoWidth * 501 * sizeof(int)));
    CUDA_CHECK(cudaMalloc(&d_headBoxes, headDetectionResults.size() * sizeof(Box)));

    // Create alpha texture
    cudaChannelFormatDesc channelDesc = cudaCreateChannelDesc<float>();
    CUDA_CHECK(cudaMallocArray(&d_alphaArray, &channelDesc, videoWidth, videoHeight));

    cudaResourceDesc resDesc = {};
    resDesc.resType = cudaResourceTypeArray;
    resDesc.res.array.array = d_alphaArray;

    cudaTextureDesc texDesc = {};
    texDesc.addressMode[0] = cudaAddressModeClamp;
    texDesc.addressMode[1] = cudaAddressModeClamp;
    texDesc.filterMode = cudaFilterModeLinear;
    texDesc.readMode = cudaReadModeElementType;
    texDesc.normalizedCoords = 0;

    CUDA_CHECK(cudaCreateTextureObject(&d_originalAlphaTexture, &resDesc, &texDesc, nullptr));

    // Allocate background estimation buffers
    size_t backgroundDataSize = getBackgroundDataBufferSize(videoWidth, videoHeight);
    CUDA_CHECK(cudaMalloc(&d_horizontalBgData, backgroundDataSize));
    CUDA_CHECK(cudaMalloc(&d_verticalBgData, backgroundDataSize));

    return true;
}

bool FrameProcessor::ProcessFrame(void* inputNv12Data, size_t inputPitch, float* outputRgbaData) {
    // Input and output data are already on GPU, no need to copy
    // Convert NV12 to RGB directly from input GPU data
    launchByteNv12ToPlanarFloatRgb(static_cast<unsigned char*>(inputNv12Data), initialImageMatter->GetInputBuffer(), videoWidth, videoHeight, inputPitch, processingStream);
    CUDA_CHECK(cudaStreamSynchronize(processingStream));

    //SavePlanarFloatImageToPNG("Videos\\input_rgb.png", initialImageMatter->GetInputBuffer(), videoWidth, videoHeight, false, processingStream);

    // Run InsPyReNet for initial alpha matte
    if (!initialImageMatter->Infer()) {
        std::cerr << "Failed to run initial image matter inference" << std::endl;
        return false;
    }

    //SaveAlphaMatteToPNG("Videos\\alpha_matte.png", initialImageMatter->GetOutputBuffer(), videoWidth, videoHeight);

    // Copy alpha buffer to texture for processing
    CUDA_CHECK(cudaMemcpy2DToArray(d_alphaArray, 0, 0, initialImageMatter->GetOutputBuffer(), videoWidth * sizeof(float), videoWidth * sizeof(float), videoHeight, cudaMemcpyDeviceToDevice));

    // Detect heads
    if (!headDetector->DetectHeads(initialImageMatter->GetInputBuffer(), videoWidth, videoHeight, headDetectionResults, 0.5f)) {
        std::cerr << "Failed to detect heads" << std::endl;
        return false;
    }

    // Rescan each head region to tightly fit non-background pixels (except neck direction)
	launchRescanHeadRegions(
		initialImageMatter->GetInputBuffer(),
		initialImageMatter->GetOutputBuffer(),
		d_headBoxes,
		static_cast<int>(headDetectionResults.size()),
		videoWidth,
		videoHeight,
		0.0f / 255.0f, // bgThreshold
		processingStream);
	CUDA_CHECK(cudaStreamSynchronize(processingStream));

	// Process head boxes (extend regions)
	for (auto& head : headDetectionResults) {
		if (head.width <= 0 || head.height <= 0)
			continue;
		float extension = 5; // Just to have some background everywhere for the next head image mating.
		head.x -= 5;
		head.y -= 5;
		head.width += 2 * 5;
		head.height += 1 * 5; // NB: We don't increase in the direction of the neck.
	}

    // Copy head boxes to device
    CUDA_CHECK(cudaMemcpy(d_headBoxes, headDetectionResults.data(), headDetectionResults.size() * sizeof(Box), cudaMemcpyHostToDevice));

    // Generate trimap
    launchGenerateTrimap(d_trimapBuffer, initialImageMatter->GetOutputBuffer(), videoWidth, videoHeight, processingStream);
    CUDA_CHECK(cudaStreamSynchronize(processingStream));

    //SaveAlphaMatteToPNG("Videos\\alpha_trimap.png", d_trimapBuffer, videoWidth, videoHeight);

    CUcontext mainContext;
    cuCtxGetCurrent(&mainContext);

#if true // Parallel processing is actually slower because of limited available memory.

    ProcessBodyRegions();
    ProcessHeadRegions();

#else

    // Process body and hair regions in parallel using separate streams with proper CUDA context
    auto bodyFuture = std::async(std::launch::async, [this, mainContext]() {
        // Set CUDA context for this thread
        CUresult contextResult = cuCtxSetCurrent(mainContext);
        if (contextResult != CUDA_SUCCESS) {
            printf("ERROR: Failed to set CUDA context in body thread: %d\n", contextResult);
            return;
        }
        ProcessBodyRegions();
    });

    auto headFuture = std::async(std::launch::async, [this, mainContext]() {
        // Set CUDA context for this thread
        CUresult contextResult = cuCtxSetCurrent(mainContext);
        if (contextResult != CUDA_SUCCESS) {
            printf("ERROR: Failed to set CUDA context in hair thread: %d\n", contextResult);
            return;
        }
        ProcessHairRegions();
    });

    // Wait for both processing tasks to complete
    bodyFuture.wait();
    headFuture.wait();

#endif

    //SaveAlphaMatteToPNG("Videos\\alpha_trimap_improved.png", d_trimapBuffer, videoWidth, videoHeight);

    //SaveAlphaMatteToPNG("Videos\\alpha02 with head.png", initialImageMatter->GetOutputBuffer(), videoWidth, videoHeight);

    // Estimate background using three-kernel approach

    // Kernel 1: Horizontal background estimation
    launchEstimateBackgroundHorizontal(
        initialImageMatter->GetOutputBuffer(),  // Alpha matte
        initialImageMatter->GetInputBuffer(),   // Original RGB data (planar format)
        d_horizontalBgData,
        videoWidth, videoHeight,
        processingStream
    );

    // Kernel 2: Vertical background estimation
    launchEstimateBackgroundVertical(
        initialImageMatter->GetOutputBuffer(),  // Alpha matte
        initialImageMatter->GetInputBuffer(),   // Original RGB data (planar format)
        d_verticalBgData,
        videoWidth, videoHeight,
        processingStream
    );

    // Kernel 3: Extract foreground using estimated background and alpha compositing formula
    launchExtractForeground(
        initialImageMatter->GetOutputBuffer(),  // Alpha matte
        initialImageMatter->GetInputBuffer(),   // Original RGB data (planar format)
        d_horizontalBgData,
        d_verticalBgData,
		outputRgbaData,
        videoWidth, videoHeight,
        processingStream
    );

    CUDA_CHECK(cudaStreamSynchronize(processingStream));
	//SaveInterleavedFloatImageToPNG("Videos\\output_rgba.png", outputRgbaData, videoWidth, videoHeight, true);

    return true;
}

bool FrameProcessor::FindInitialAlpha(void* inputNv12Data, size_t inputPitch, unsigned char** encodedAlpha, size_t* encodedSize) {
    // Input and output data are already on GPU, no need to copy
    // Convert NV12 to RGB directly from input GPU data
    launchByteNv12ToPlanarFloatRgb(static_cast<unsigned char*>(inputNv12Data), initialImageMatter->GetInputBuffer(), videoWidth, videoHeight, inputPitch, processingStream);
    CUDA_CHECK(cudaStreamSynchronize(processingStream));

    // Run initial image matting for alpha matte
    if (!initialImageMatter->Infer()) {
        std::cerr << "Failed to run initial image matter inference" << std::endl;
        return false;
    }

    // Convert float alpha to uint8 for compression
    size_t alphaBufferSize = videoWidth * videoHeight;
    unsigned char* d_alphaUint8 = nullptr;
    CUDA_CHECK(cudaMalloc(&d_alphaUint8, alphaBufferSize));

    // Convert float alpha (0.0-1.0) to uint8 alpha (0-255)
    launchFloatToUint8Alpha(initialImageMatter->GetOutputBuffer(), d_alphaUint8, videoWidth, videoHeight, processingStream);
    CUDA_CHECK(cudaStreamSynchronize(processingStream));

    // Compress alpha using alpha_codec
    CompressedAlphaData* compressedData = compress_alpha_cuda(d_alphaUint8, videoWidth, videoHeight, processingStream);
    if (!compressedData) {
        std::cerr << "Failed to compress alpha data" << std::endl;
        cudaFree(d_alphaUint8);
        return false;
    }

    // Copy compressed data to output
    *encodedSize = compressedData->compressed_size;
    *encodedAlpha = static_cast<unsigned char*>(malloc(*encodedSize));
    if (!*encodedAlpha) {
        std::cerr << "Failed to allocate memory for encoded alpha" << std::endl;
        free_compressed_alpha_data(compressedData);
        cudaFree(d_alphaUint8);
        return false;
    }
    memcpy(*encodedAlpha, compressedData->compressed_data, *encodedSize);

    // Cleanup
    free_compressed_alpha_data(compressedData);
    cudaFree(d_alphaUint8);

    return true;
}

bool FrameProcessor::RefineInitialAlpha(void* inputNv12Data, size_t inputPitch, unsigned char* encodedAlpha, size_t encodedSize, float* outputRgbaData) {
    // Create CompressedAlphaData structure from encoded data
    CompressedAlphaData compressedData;
    compressedData.compressed_data = encodedAlpha;
    compressedData.compressed_size = encodedSize;
    compressedData.width = videoWidth;
    compressedData.height = videoHeight;
    compressedData.alpha_length = videoWidth * videoHeight;

    // Allocate device buffer for decompressed alpha
    unsigned char* d_decompressedAlpha = nullptr;
    CUDA_CHECK(cudaMalloc(&d_decompressedAlpha, videoWidth * videoHeight * sizeof(unsigned char)));

    // Decompress alpha using alpha_codec
    cudaError_t result = decompress_alpha_cuda(&compressedData, d_decompressedAlpha, processingStream);
    if (result != cudaSuccess) {
        std::cerr << "Failed to decompress alpha data: " << cudaGetErrorString(result) << std::endl;
        cudaFree(d_decompressedAlpha);
        return false;
    }

    // Convert uint8 alpha back to float alpha
    float* d_alphaFloat = nullptr;
    size_t alphaBufferSize = videoWidth * videoHeight * sizeof(float);
    CUDA_CHECK(cudaMalloc(&d_alphaFloat, alphaBufferSize));

    // Convert uint8 alpha (0-255) to float alpha (0.0-1.0)
    launchUint8ToFloatAlpha(d_decompressedAlpha, d_alphaFloat, videoWidth, videoHeight, processingStream);
    CUDA_CHECK(cudaStreamSynchronize(processingStream));

    // Convert NV12 to RGB for IndexNet processing
    launchByteNv12ToPlanarFloatRgb(static_cast<unsigned char*>(inputNv12Data), indexNetImageMatter->GetInputBuffer(), videoWidth, videoHeight, inputPitch, processingStream);
    CUDA_CHECK(cudaStreamSynchronize(processingStream));

    // Convert NV12 to RGB for IndexNet processing
    launchByteNv12ToPlanarFloatRgb(static_cast<unsigned char*>(inputNv12Data), indexNetImageMatter->GetInputBuffer(), videoWidth, videoHeight, inputPitch, processingStream);
    CUDA_CHECK(cudaStreamSynchronize(processingStream));

    // Run head detection for uncertain region processing
    if (!headDetector->DetectHeads(indexNetImageMatter->GetInputBuffer(), videoWidth, videoHeight, headDetectionResults, 0.5f)) {
        std::cerr << "Failed to detect heads" << std::endl;
        cudaFree(d_decompressedAlpha);
        cudaFree(d_alphaFloat);
        return false;
    }

    // Copy head boxes to device
    CUDA_CHECK(cudaMemcpy(d_headBoxes, headDetectionResults.data(), headDetectionResults.size() * sizeof(Box), cudaMemcpyHostToDevice));

    // Generate trimap from refined alpha
    launchGenerateTrimap(d_trimapBuffer, d_alphaFloat, videoWidth, videoHeight, processingStream);
    CUDA_CHECK(cudaStreamSynchronize(processingStream));

    // Process body and head regions for refinement (simplified version for two-phase)
    ProcessBodyRegions();
    ProcessHeadRegions();

    // Estimate background using three-kernel approach
    // Kernel 1: Horizontal background estimation
    launchEstimateBackgroundHorizontal(
        d_alphaFloat,  // Refined alpha matte
        indexNetImageMatter->GetInputBuffer(),   // Original RGB data (planar format)
        d_horizontalBgData,
        videoWidth, videoHeight,
        processingStream
    );

    // Kernel 2: Vertical background estimation
    launchEstimateBackgroundVertical(
        d_alphaFloat,  // Refined alpha matte
        indexNetImageMatter->GetInputBuffer(),   // Original RGB data (planar format)
        d_verticalBgData,
        videoWidth, videoHeight,
        processingStream
    );

    // Kernel 3: Extract foreground using estimated background and alpha compositing formula
    launchExtractForeground(
        d_alphaFloat,  // Refined alpha matte
        indexNetImageMatter->GetInputBuffer(),   // Original RGB data (planar format)
        d_horizontalBgData,
        d_verticalBgData,
        outputRgbaData,
        videoWidth, videoHeight,
        processingStream
    );
    CUDA_CHECK(cudaStreamSynchronize(processingStream));

    // Cleanup
    cudaFree(d_decompressedAlpha);
    cudaFree(d_alphaFloat);

    return true;
}

void FrameProcessor::ProcessBodyRegions() {
    auto indexNetMatterBody = ImageMattingFactory::GetInstance(
        ModelType::INDEXNET, IndexNetModelInputSize, IndexNetModelInputSize, bodyStream,
        InferenceBackend::AUTO, ImageMattingFactory::BestModelSelectionMethod::SMALLEST_AREA_FIT, false);
    // If useModelSize is true, you may want to update IndexNetModelInputSize, but here we keep the region size logic as is.

    if (indexNetMatterBody != nullptr) {
        // Initialize the regions buffer to ensure clean state
        cudaMemsetAsync(d_regionsBufferBody, 0, regionsBufferSizeBody, bodyStream);
        cudaStreamSynchronize(bodyStream);

        // Detect alpha border pixels in vertical sections for body
        launchDetectUncertainRegions(
            initialImageMatter->GetOutputBuffer(),
            d_regionsBufferBody,
            videoWidth,
            videoHeight,
            regionSizeBody,
            bodyStream
        );
        cudaStreamSynchronize(bodyStream);

        // Copy regions buffer from device to host
        cudaMemcpy(h_regionsBufferBody, d_regionsBufferBody, regionsBufferSizeBody, cudaMemcpyDeviceToHost);

        //SaveAlphaMatteToPNG("Videos\\alphaRegionsBody.png", h_regionsBufferBody, videoWidth, NumVerticalRegionCountBody);

        // Process regular regions (non-head regions)
        for (int yRegion = 0; yRegion < (videoHeight + regionSizeBody - 1) / regionSizeBody; yRegion++) {
            for (int x = 0; x < videoWidth; x++) {
                if (h_regionsBufferBody[yRegion * videoWidth + x]) {
                    if (isInsideHeadRegions(x, yRegion, headDetectionResults, regionSizeBody, videoWidth)) continue;

                    // Prepare input for IndexNet inference using trimap
                    launchPrepareIndexNetInput(
                        indexNetMatterBody->GetInputBuffer(),
                        initialImageMatter->GetInputBuffer(),
                        d_trimapBuffer,
                        x, yRegion * regionSizeBody,
                        videoWidth, videoHeight,
                        IndexNetModelInputSize, IndexNetModelInputSize,
                        MaxUncertainRegionWidthForBody,
                        bodyStream
                    );
                    cudaStreamSynchronize(bodyStream);

                    // IndexNet inference
                    if (!indexNetMatterBody->Infer()) {
                        std::cerr << "Failed to process IndexNet matting at region " << x << "," << yRegion << std::endl;
                        continue;
                    }

                    //SaveAlphaMatteToPNG("Videos\\alpha01.png", indexNetMatterBody->GetOutputBuffer(), IndexNetModelInputSize, IndexNetModelInputSize);

                    // Update alpha buffer region
                    launchUpdateAlphaBufferRegion(
                        initialImageMatter->GetOutputBuffer(),
                        d_originalAlphaTexture,
                        d_trimapBuffer,  // Use trimap for blending guidance
                        indexNetMatterBody->GetOutputBuffer(),
                        x, yRegion * regionSizeBody,
                        videoWidth, videoHeight,
                        IndexNetModelInputSize,
                        MaxUncertainRegionWidthForBody,
                        bodyStream
                    );
                    cudaStreamSynchronize(bodyStream);

                    //SaveAlphaMatteToPNG("Videos\\alpha02.png", initialImageMatter->GetOutputBuffer(), videoWidth, videoHeight);

                    x += IndexNetModelInputSize - 2 * MaxUncertainRegionWidthForBody;
                }
            }
        }

        // Return the IndexNet instance when done with it
        ImageMattingFactory::ReturnInstance(indexNetMatterBody);
    }
}

void FrameProcessor::ProcessHeadRegions() {
	// Process each detected head region with InsPyReNet for better hair alpha matte precision
	for (const auto& head : headDetectionResults) {
		if (head.width <= 0 || head.height <= 0) continue;

		// Calculate head region bounds with padding for better hair capture
		int regionLeft = std::max(0, static_cast<int>(head.x) - 50);  // Add padding for hair
		int regionTop = std::max(0, static_cast<int>(head.y) - 50);
		int regionRight = std::min(videoWidth, static_cast<int>(head.x + head.width) + 50);
		int regionBottom = std::min(videoHeight, static_cast<int>(head.y + head.height) + 50);

		int regionWidth = regionRight - regionLeft;
		int regionHeight = regionBottom - regionTop;

        regionWidth = 1280;
        regionHeight = 720;

		if (regionWidth <= 0 || regionHeight <= 0) continue;

		// Use InsPyReNet for whole head region processing with TensorRT preference for better hair alpha matte precision
		auto insPyReNetMatterHair = ImageMattingFactory::GetInstance(
			ModelType::INSPYRENET,
            regionWidth, regionHeight,
			headStream,
			InferenceBackend::TENSORRT_WITH_FALLBACK,
            //InferenceBackend::ONNX_ONLY,
            ImageMattingFactory::BestModelSelectionMethod::ASPECT_RATIO_CLOSEST_FIT,
            true // We take an image area of the size of the best model found.
		);
		if (insPyReNetMatterHair != nullptr) {
            // We take an image area of the size of the best model found.
            regionWidth = insPyReNetMatterHair->GetModelWidth();
            regionHeight = insPyReNetMatterHair->GetModelHeight();

			// Extract head region only (no resizing); Infer() will handle resizing if needed
			launchExtractHeadRegion(
			    initialImageMatter->GetInputBuffer(),
			    insPyReNetMatterHair->GetInputBuffer(),
			    regionLeft, regionTop, regionWidth, regionHeight,
			    videoWidth, videoHeight,
			    headStream
			    );
			cudaStreamSynchronize(headStream);

			//SavePlanarFloatImageToPNG("Videos\\input_rgb_head.png", insPyReNetMatterHair->GetInputBuffer(), regionWidth, regionHeight, false, headStream);

			// Run InsPyReNet inference on the whole head region for better hair alpha matte precision
			if (!insPyReNetMatterHair->Infer()) {
				std::cerr << "Failed to process InsPyReNet matting for head region" << std::endl;
				continue;
			}

			//SaveAlphaMatteToPNG("Videos\\alpha_head.png", insPyReNetMatterHair->GetOutputBuffer(), regionWidth, regionHeight);

			// Update alpha buffer from head region result
			launchUpdateAlphaFromHeadRegion(
				initialImageMatter->GetOutputBuffer(),
				insPyReNetMatterHair->GetOutputBuffer(),
				d_trimapBuffer,  // Use trimap for blending guidance
				regionLeft, regionTop,
				videoWidth, videoHeight,
                regionWidth, regionHeight,  // Source size from InsPyReNet
				headStream
			);
			cudaStreamSynchronize(headStream);
            //SaveAlphaMatteToPNG("Videos\\alpha_all.png", initialImageMatter->GetOutputBuffer(), videoWidth, videoHeight);

            // Return the InsPyReNet instance when done with it
            ImageMattingFactory::ReturnInstance(insPyReNetMatterHair);
		}
	}
}

void FrameProcessor::DeallocateBuffers() {
    if (d_trimapBuffer) {
        cudaFree(d_trimapBuffer);
        d_trimapBuffer = nullptr;
    }
    if (d_regionsBufferBody) {
        cudaFree(d_regionsBufferBody);
        d_regionsBufferBody = nullptr;
    }
    if (d_regionsBufferHair) {
        cudaFree(d_regionsBufferHair);
        d_regionsBufferHair = nullptr;
    }
    if (d_edgePositions) {
        cudaFree(d_edgePositions);
        d_edgePositions = nullptr;
    }
    if (d_headBoxes) {
        cudaFree(d_headBoxes);
        d_headBoxes = nullptr;
    }
    if (d_originalAlphaTexture) {
        cudaDestroyTextureObject(d_originalAlphaTexture);
        d_originalAlphaTexture = 0;
    }
    if (d_alphaArray) {
        cudaFreeArray(d_alphaArray);
        d_alphaArray = nullptr;
    }

    // Free background estimation buffers
    if (d_horizontalBgData) {
        cudaFree(d_horizontalBgData);
        d_horizontalBgData = nullptr;
    }
    if (d_verticalBgData) {
        cudaFree(d_verticalBgData);
        d_verticalBgData = nullptr;
    }

    // Free host memory
    if (h_regionsBufferBody) {
        delete[] h_regionsBufferBody;
        h_regionsBufferBody = nullptr;
    }
    if (h_regionsBufferHair) {
        delete[] h_regionsBufferHair;
        h_regionsBufferHair = nullptr;
    }
}

void FrameProcessor::Cleanup() {
    DeallocateBuffers();

    if (bodyStream) {
        cudaStreamDestroy(bodyStream);
        bodyStream = nullptr;
    }
    if (headStream) {
        cudaStreamDestroy(headStream);
        headStream = nullptr;
    }
    // Note: processingStream is managed by main.cpp, don't destroy it here
}
