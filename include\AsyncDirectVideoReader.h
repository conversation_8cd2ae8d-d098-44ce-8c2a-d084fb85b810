#pragma once

#include <string>
#include <memory>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <cuda.h>
#include <cuda_runtime.h>
#include "DirectVideoReader.h"

/**
 * Asynchronous video reader with pre-buffering for non-blocking frame access
 * Wraps DirectVideoReader and maintains a ready frame buffer so processing never waits
 */
class AsyncDirectVideoReader {
public:
    /**
     * Structure to hold a buffered frame
     */
    struct BufferedFrame {
        void* cudaBuffer;           // CUDA memory buffer containing frame data
        size_t bufferSize;          // Size of the buffer
        size_t bufferPitch;         // Pitch of the buffer
        double timestamp;           // Frame timestamp
        bool isValid;               // Whether this frame contains valid data
        bool isEndOfStream;         // Whether this indicates end of stream

        BufferedFrame()
            : cudaBuffer(nullptr), bufferSize(0), bufferPitch(0),
              timestamp(-1.0), isValid(false), isEndOfStream(false) {}

        ~BufferedFrame() {
            if (cudaBuffer) {
                cudaFree(cudaBuffer);
                cudaBuffer = nullptr;
            }
        }

        // Move constructor
        BufferedFrame(BufferedFrame&& other) noexcept
            : cudaBuffer(other.cudaBuffer), bufferSize(other.bufferSize),
              bufferPitch(other.bufferPitch), timestamp(other.timestamp),
              isValid(other.isValid), isEndOfStream(other.isEndOfStream) {
            other.cudaBuffer = nullptr;
            other.bufferSize = 0;
            other.bufferPitch = 0;
            other.timestamp = -1.0;
            other.isValid = false;
            other.isEndOfStream = false;
        }

        // Move assignment operator
        BufferedFrame& operator=(BufferedFrame&& other) noexcept {
            if (this != &other) {
                if (cudaBuffer) {
                    cudaFree(cudaBuffer);
                }
                cudaBuffer = other.cudaBuffer;
                bufferSize = other.bufferSize;
                bufferPitch = other.bufferPitch;
                timestamp = other.timestamp;
                isValid = other.isValid;
                isEndOfStream = other.isEndOfStream;

                other.cudaBuffer = nullptr;
                other.bufferSize = 0;
                other.bufferPitch = 0;
                other.timestamp = -1.0;
                other.isValid = false;
                other.isEndOfStream = false;
            }
            return *this;
        }

        // Delete copy constructor and assignment
        BufferedFrame(const BufferedFrame&) = delete;
        BufferedFrame& operator=(const BufferedFrame&) = delete;
    };

    /**
     * Create an asynchronous video reader
     * @param filePath Path to video file
     * @param cudaContext CUDA context to use
     * @param enableHardwareAcceleration Whether to use hardware acceleration
     * @param bufferSize Number of frames to buffer (default: 3)
     * @return Shared pointer to reader instance, nullptr on failure
     */
    static std::shared_ptr<AsyncDirectVideoReader> Create(
        const std::string& filePath,
        CUcontext cudaContext,
        bool enableHardwareAcceleration = true,
        int bufferSize = 3);

    /**
     * Destructor - stops background thread and cleans up
     */
    ~AsyncDirectVideoReader();

    /**
     * Start asynchronous reading (called automatically by Create)
     * @return True on success, false on failure
     */
    bool StartAsync();

    /**
     * Stop asynchronous reading and wait for thread to finish
     */
    void StopAsync();

    /**
     * Read the next frame (non-blocking if frames are buffered)
     * @param cudaBuffer Output buffer for frame data
     * @param bufferSize Size of output buffer
     * @param bufferPitch Pitch of output buffer
     * @return Frame timestamp, or -1.0 on end of stream/error
     */
    double ReadFrame(void* cudaBuffer, size_t bufferSize, size_t bufferPitch);

    /**
     * Read the next frame (simplified version for compatibility)
     * @param cudaBuffer Pointer to output buffer pointer
     * @param bufferSize Reference to buffer size
     * @return Frame timestamp, or -1.0 on end of stream/error
     */
    double ReadFrame(void** cudaBuffer, size_t& bufferSize);

    /**
     * Seek to a specific time position
     * Note: This will clear the buffer and restart async reading
     * @param timeInSeconds Time position in seconds
     * @return True on success, false on failure
     */
    bool Seek(double timeInSeconds);

    /**
     * Reset the reader to the beginning of the video
     * @return True on success, false on failure
     */
    bool Reset() { return Seek(0.0); }

    // Video property accessors (delegate to underlying reader)
    int GetWidth() const { return m_reader ? m_reader->GetWidth() : 0; }
    int GetHeight() const { return m_reader ? m_reader->GetHeight() : 0; }
    double GetDuration() const { return m_reader ? m_reader->GetDuration() : 0.0; }
    AVRational GetFrameRate() const { return m_reader ? m_reader->GetFrameRate() : AVRational{0, 1}; }
    double GetFrameRateDouble() const { return m_reader ? m_reader->GetFrameRateDouble() : 0.0; }
    VideoUtils::TextureFormat GetTextureFormat() const {
        return m_reader ? m_reader->GetTextureFormat() : VideoUtils::TextureFormat::Unknown;
    }
    AVRational GetTimeBase() const { return m_reader ? m_reader->GetTimeBase() : AVRational{0, 1}; }
    double GetTimeBaseDouble() const { return m_reader ? m_reader->GetTimeBaseDouble() : 0.0; }
    VideoUtils::TextureFormat GetVideoStreamFormat() const {
        return m_reader ? m_reader->GetVideoStreamFormat() : VideoUtils::TextureFormat::Unknown;
    }
    int GetFrameCount() const {
        if (!m_reader) return 0;
        double duration = m_reader->GetDuration();
        double frameRate = m_reader->GetFrameRateDouble();
        return static_cast<int>(duration * frameRate + 0.5);
    }

    /**
     * Get the current buffer fill level
     * @return Number of frames currently buffered
     */
    int GetBufferFillLevel() const;

    /**
     * Check if there are any buffering errors
     * @return True if there are errors, false otherwise
     */
    bool HasBufferingError() const { return m_bufferingError.load(); }

    /**
     * Check if the reader is initialized
     * @return True if initialized, false otherwise
     */
    bool IsInitialized() const { return m_initialized; }

private:
    // Private constructor
    AsyncDirectVideoReader();

    // Initialize the reader
    bool Initialize(const std::string& filePath, CUcontext cudaContext,
                   bool enableHardwareAcceleration, int bufferSize);

    // Background thread function for reading frames
    void ReadingThreadFunction();

    // Clear the frame buffer
    void ClearBuffer();

    // Member variables
    std::shared_ptr<DirectVideoReader> m_reader;
    CUcontext m_cudaContext;

    // Threading
    std::thread m_readingThread;
    std::atomic<bool> m_stopReading{false};
    std::atomic<bool> m_bufferingError{false};
    std::atomic<bool> m_seekRequested{false};
    std::atomic<double> m_seekTime{0.0};

    // Frame buffer
    std::queue<std::unique_ptr<BufferedFrame>> m_frameBuffer;
    mutable std::mutex m_bufferMutex;
    std::condition_variable m_bufferCondition;
    std::condition_variable m_readerCondition;

    // Configuration
    int m_maxBufferSize;
    bool m_initialized;

    // Frame properties for buffer allocation
    size_t m_frameBufferSize;
    size_t m_frameBufferPitch;
};
