#pragma once

#include "ProcessingStep.h"
#include "FrameMetadata.h"
#include "HeadDetector.h"
#include <memory>
#include <vector>

/**
 * Processing step for head detection
 * Uses HeadDetector to find head regions in the frame
 */
class DetectHeadsStep : public ProcessingStep {
public:
    DetectHeadsStep();
    virtual ~DetectHeadsStep();

    // ProcessingStep interface implementation
    bool Initialize(int width, int height, CUcontext cudaContext, cudaStream_t stream) override;
    bool Process(void* inputNv12Data, size_t inputPitch, FrameMetadata& frameMetadata) override;
    const char* GetStepName() const override { return "DetectHeads"; }
    bool IsInitialized() const override { return m_initialized; }

private:
    // Head detector instance
    std::unique_ptr<HeadDetector> m_headDetector;

    // RGB buffer for head detection input
    float* m_rgbBuffer;
    size_t m_rgbBufferSize;

    // Processing parameters
    int m_width;
    int m_height;
    CUcontext m_cudaContext;
    cudaStream_t m_stream;
    bool m_initialized;

    // Detection results
    std::vector<Box> m_detectionResults;

    // Helper methods
    bool AllocateBuffers();
    void DeallocateBuffers();
};
