// DirectVideoReader.cpp
#include "DirectVideoReader.h"
#include "DirectVideoWriter.h"
#include "main_Kernels.cuh"
#include "ImageMattingOnnx.h"

extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/hwcontext.h>
#include <libavutil/hwcontext_cuda.h>
#include <libavutil/error.h>
#include <libavutil/imgutils.h>
}

#include <stdexcept>
#include <iostream>
#include <algorithm>
#include <cmath>

#pragma comment(lib, "avcodec.lib")
#pragma comment(lib, "avformat.lib")
#pragma comment(lib, "avutil.lib")
#pragma comment(lib, "cuda.lib")
#pragma comment(lib, "cudart.lib")

// Helper function for error handling
static void CheckFFmpegError(int error) {
    if (error < 0) {
        char errBuf[AV_ERROR_MAX_STRING_SIZE] = { 0 };
        av_strerror(error, errBuf, AV_ERROR_MAX_STRING_SIZE);
        throw std::runtime_error(std::string("FFmpeg error: ") + errBuf);
    }
}

// Helper function for CUDA error handling
static void CheckCudaError(cudaError_t error) {
    if (error != cudaSuccess) {
        throw std::runtime_error(std::string("CUDA error: ") + cudaGetErrorString(error));
    }
}

DirectVideoReader::DirectVideoReader() : 
    m_formatContext(nullptr),
    m_codecContext(nullptr),
    m_hwFrame(nullptr),
    m_packet(nullptr),
    m_videoStreamIndex(-1),
    m_isHardwareAccelerated(false),
    m_cudaContext(nullptr),
    m_cudaStream(nullptr),
    m_cudaFrameBuffer(nullptr),
    m_cudaFrameBufferSize(0),
    m_yuv420pConversionBuffer(nullptr),
    m_yuv420pConversionBufferSize(0),
    m_width(0),
    m_height(0),
    m_duration(0.0),
    m_frameRate({ 0, 1 }),
    m_timeBase({ 0, 1 }),
    m_textureFormat(VideoUtils::TextureFormat::Unknown),
    m_sourceFormat(VideoUtils::TextureFormat::Unknown),
    m_currentFrameTimestamp(0),
    m_isInitialized(false),
    m_isEof(false) {
}

DirectVideoReader::~DirectVideoReader() {
    Close();
}

std::shared_ptr<DirectVideoReader> DirectVideoReader::Create(
    const std::string& filePath,
    CUcontext cudaContext,
    bool enableHardwareAcceleration) {

    std::shared_ptr<DirectVideoReader> reader(new DirectVideoReader());

    if (!reader->Initialize(filePath, cudaContext, enableHardwareAcceleration)) {
        return nullptr;
    }

    return reader;
}

VideoUtils::TextureFormat DirectVideoReader::GetVideoStreamFormat() const {
    if (!m_isInitialized) return VideoUtils::TextureFormat::Unknown;
    return m_sourceFormat;
}

bool DirectVideoReader::Initialize(const std::string& filePath, CUcontext cudaContext, bool enableHardwareAcceleration) {
    // Force hardware acceleration to true as per requirements
    enableHardwareAcceleration = true;

    try {
        // Initialize FFmpeg
        static bool ffmpegInitialized = false;
        if (!ffmpegInitialized) {
            avformat_network_init();
            ffmpegInitialized = true;
        }

        // Open video file
        CheckFFmpegError(avformat_open_input(&m_formatContext, filePath.c_str(), nullptr, nullptr));

        // Retrieve stream information
        CheckFFmpegError(avformat_find_stream_info(m_formatContext, nullptr));

        // Find the first video stream
        m_videoStreamIndex = -1;
        for (unsigned int i = 0; i < m_formatContext->nb_streams; i++) {
            if (m_formatContext->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
                m_videoStreamIndex = i;
                break;
            }
        }

        if (m_videoStreamIndex == -1) {
            throw std::runtime_error("No video stream found");
        }

        // Get video properties
        AVStream* videoStream = m_formatContext->streams[m_videoStreamIndex];
        m_width = videoStream->codecpar->width;
        m_height = videoStream->codecpar->height;
        m_duration = static_cast<double>(m_formatContext->duration) / AV_TIME_BASE;
        m_frameRate = videoStream->avg_frame_rate;
        m_timeBase = videoStream->time_base;

        // Determine source format
        AVPixelFormat pixFmt = static_cast<AVPixelFormat>(videoStream->codecpar->format);
        if (pixFmt == AV_PIX_FMT_YUV420P) {
            m_sourceFormat = VideoUtils::TextureFormat::YUV420P;
        }
        else {
            // Most hardware decoders output NV12, so we'll assume NV12 for hardware acceleration
            m_sourceFormat = VideoUtils::TextureFormat::NV12;
        }

        // Find decoder
        const AVCodec* decoder = avcodec_find_decoder(videoStream->codecpar->codec_id);
        if (!decoder) {
            throw std::runtime_error("Unsupported codec");
        }

        // Allocate codec context
        m_codecContext = avcodec_alloc_context3(decoder);
        if (!m_codecContext) {
            throw std::runtime_error("Could not allocate codec context");
        }

        // Copy codec parameters to codec context
        CheckFFmpegError(avcodec_parameters_to_context(m_codecContext, videoStream->codecpar));

        // Setup hardware acceleration
        m_isHardwareAccelerated = true;
        AVBufferRef* hwDeviceCtx = nullptr;
        m_cudaContext = cudaContext; // Use the user-provided CUDA context

        // Manually create the FFmpeg hardware device context to use the existing CUDA context
        hwDeviceCtx = av_hwdevice_ctx_alloc(AV_HWDEVICE_TYPE_CUDA);
        if (!hwDeviceCtx) {
            throw std::runtime_error("Failed to allocate FFmpeg CUDA hardware context.");
        }
        AVHWDeviceContext* device_ctx = (AVHWDeviceContext*)hwDeviceCtx->data;
        AVCUDADeviceContext* cuda_device_ctx = (AVCUDADeviceContext*)device_ctx->hwctx;
        cuda_device_ctx->cuda_ctx = m_cudaContext;

        CheckFFmpegError(av_hwdevice_ctx_init(hwDeviceCtx));
        m_codecContext->hw_device_ctx = av_buffer_ref(hwDeviceCtx);

        // Set hardware acceleration flags
        m_codecContext->get_format = [](AVCodecContext* ctx, const enum AVPixelFormat* pix_fmts) -> enum AVPixelFormat {
            const enum AVPixelFormat* p;
            for (p = pix_fmts; *p != AV_PIX_FMT_NONE; p++) {
                if (*p == AV_PIX_FMT_CUDA)
                    return *p;
            }
            return AV_PIX_FMT_NONE;
            };

        // Create CUDA stream within the provided context
        CheckCudaError(cudaStreamCreate(&m_cudaStream));

        // Allocate CUDA frame buffer (NV12 format: Y plane + UV plane)
        m_cudaFrameBufferSize = m_width * m_height * 3 / 2; // NV12 is 1.5 bytes per pixel
        CheckCudaError(cudaMalloc(&m_cudaFrameBuffer, m_cudaFrameBufferSize));

        // Allocate YUV420P conversion buffer if needed
        if (m_sourceFormat == VideoUtils::TextureFormat::YUV420P) {
            m_yuv420pConversionBufferSize = m_cudaFrameBufferSize;
            CheckCudaError(cudaMalloc(&m_yuv420pConversionBuffer, m_yuv420pConversionBufferSize));
        }

        // Open codec
        CheckFFmpegError(avcodec_open2(m_codecContext, decoder, nullptr));

        // Set NV12 as the target texture format
        m_textureFormat = VideoUtils::TextureFormat::NV12;

        // Allocate frames and packet
        m_hwFrame = av_frame_alloc();
        m_packet = av_packet_alloc();

        if (!m_hwFrame || !m_packet) {
            throw std::runtime_error("Could not allocate frame or packet");
        }

        // Clean up hardware device context
        if (hwDeviceCtx) {
            av_buffer_unref(&hwDeviceCtx);
        }

        m_isInitialized = true;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error initializing DirectVideoReader: " << e.what() << std::endl;
        Close();
        return false;
    }
}

bool DirectVideoReader::Seek(double timeInSeconds) {
    if (!m_isInitialized) return false;

    int64_t timestamp = static_cast<int64_t>(timeInSeconds * AV_TIME_BASE);

    // Convert timestamp to stream timebase
    timestamp = av_rescale_q(timestamp, AV_TIME_BASE_Q,
        m_formatContext->streams[m_videoStreamIndex]->time_base);

    // Seek to the specified timestamp
    int result = av_seek_frame(m_formatContext, m_videoStreamIndex, timestamp, AVSEEK_FLAG_BACKWARD);

    if (result < 0) {
        char errBuf[AV_ERROR_MAX_STRING_SIZE] = { 0 };
        av_strerror(result, errBuf, AV_ERROR_MAX_STRING_SIZE);
        std::cerr << "Error seeking: " << errBuf << std::endl;
        return false;
    }

    // Flush codec buffers
    avcodec_flush_buffers(m_codecContext);
    m_isEof = false;

    return true;
}

double DirectVideoReader::ConvertTimestampToSeconds(int64_t timestamp) const {
    if (!m_isInitialized || timestamp == AV_NOPTS_VALUE) return 0.0;

    return static_cast<double>(timestamp) * m_timeBase.num / m_timeBase.den;
}

bool DirectVideoReader::DecodePacket() {
    if (!m_isInitialized) return false;

    // Keep reading until we get a frame or reach EOF
    while (true) {
        int ret = av_read_frame(m_formatContext, m_packet);

        if (ret < 0) {
            // End of file or error
            if (ret == AVERROR_EOF) {
                m_isEof = true;
            }
            av_packet_unref(m_packet);
            return false;
        }

        // Skip non-video packets
        if (m_packet->stream_index != m_videoStreamIndex) {
            av_packet_unref(m_packet);
            continue;
        }

        // Send packet to decoder
        ret = avcodec_send_packet(m_codecContext, m_packet);

        // Unref packet as we don't need it anymore
        av_packet_unref(m_packet);

        if (ret < 0) {
            // Error sending packet
            return false;
        }

        // Get decoded frame
        ret = avcodec_receive_frame(m_codecContext, m_hwFrame);

        if (ret == AVERROR(EAGAIN)) {
            // Need more packets
            continue;
        }
        else if (ret < 0) {
            // Error receiving frame
            return false;
        }

        // Store the frame timestamp
        m_currentFrameTimestamp = m_hwFrame->pts != AV_NOPTS_VALUE ?
            m_hwFrame->pts :
            m_hwFrame->best_effort_timestamp;

        // Successfully decoded a frame
        return true;
    }
}

bool DirectVideoReader::TransferFrameToCuda(void* cudaBuffer, size_t bufferSize, size_t bufferPitch) {
    try {
        if (!m_hwFrame || !m_hwFrame->data[0]) {
            return false;
        }

        // Ensure output buffer is allocated and sized correctly
        if (!cudaBuffer || bufferSize < m_cudaFrameBufferSize) {
            std::cerr << "Wrong Cuda buffer size" << std::endl;
            return false;
        }

        // Determine the actual pixel format of the frame
        AVPixelFormat pixFormat = static_cast<AVPixelFormat>(m_hwFrame->format);
        bool isYUV420P = (pixFormat == AV_PIX_FMT_YUV420P);

        // If the frame is in YUV420P format, we need to convert it to NV12
        if (isYUV420P) {
            // If we're using the GPU decoder but the format is YUV420P, this implies
            // the frame is already on CPU and we need to convert to NV12 and transfer to GPU

            // First, transfer YUV420P data to device memory (if not already there)
            bool needToUpload = !(m_hwFrame->hw_frames_ctx); // Check if frame is not already on GPU

            if (needToUpload) {
                // Transfer the YUV420P data to the conversion buffer

                // Copy Y plane
                size_t yPlaneSize = m_width * m_height;
                CheckCudaError(cudaMemcpy2DAsync(
                    m_yuv420pConversionBuffer, m_width,
                    m_hwFrame->data[0], m_hwFrame->linesize[0],
                    m_width, m_height,
                    cudaMemcpyHostToDevice,
                    m_cudaStream
                ));

                // Copy U plane
                size_t uOffset = yPlaneSize;
                void* uDest = static_cast<unsigned char*>(m_yuv420pConversionBuffer) + uOffset;
                CheckCudaError(cudaMemcpy2DAsync(
                    uDest, m_width / 2,
                    m_hwFrame->data[1], m_hwFrame->linesize[1],
                    m_width / 2, m_height / 2,
                    cudaMemcpyHostToDevice,
                    m_cudaStream
                ));

                // Copy V plane
                size_t vOffset = yPlaneSize + (m_width * m_height / 4);
                void* vDest = static_cast<unsigned char*>(m_yuv420pConversionBuffer) + vOffset;
                CheckCudaError(cudaMemcpy2DAsync(
                    vDest, m_width / 2,
                    m_hwFrame->data[2], m_hwFrame->linesize[2],
                    m_width / 2, m_height / 2,
                    cudaMemcpyHostToDevice,
                    m_cudaStream
                ));

                // Convert from YUV420P to NV12 using our CUDA kernel
                launchYuv420pToNv12(
                    static_cast<unsigned char*>(m_yuv420pConversionBuffer),
                    static_cast<unsigned char*>(cudaBuffer),
                    m_width, m_height,
                    m_width, m_width / 2, m_width / 2,  // Source strides
                    static_cast<int>(bufferPitch),      // Destination stride (use pitch)
                    m_cudaStream
                );
                cudaError_t cudaStatus = cudaDeviceSynchronize();
                if (cudaStatus != cudaSuccess) {
                    std::cerr << "Failed to synchronize after inference: " << cudaGetErrorString(cudaStatus) << std::endl;
                    return false;
                }
            }
            else {
                // Frame is already on GPU in YUV420P format
                // Convert from YUV420P to NV12 using our CUDA kernel
                launchYuv420pToNv12(
                    static_cast<unsigned char*>(m_hwFrame->data[0]),
                    static_cast<unsigned char*>(cudaBuffer),
                    m_width, m_height,
                    m_hwFrame->linesize[0], m_hwFrame->linesize[1], m_hwFrame->linesize[2],
                    static_cast<int>(bufferPitch),  // Use pitch for destination stride
                    m_cudaStream
                );
                cudaError_t cudaStatus = cudaDeviceSynchronize();
                if (cudaStatus != cudaSuccess) {
                    std::cerr << "Failed to synchronize after inference: " << cudaGetErrorString(cudaStatus) << std::endl;
                    return false;
                }
            }
        }
        else {

            // For NV12 format, just copy the data as before
            // Copy Y plane (luma)
            size_t yPlaneSize = m_height * bufferPitch;

            CheckCudaError(cudaMemcpy2DAsync(
                cudaBuffer, bufferPitch,  // Use the pitch from the allocated buffer
                m_hwFrame->data[0], m_hwFrame->linesize[0],
                m_width, m_height,
                cudaMemcpyDeviceToDevice,
                m_cudaStream
            ));

            // Copy UV plane (chroma) - NV12 format
            void* uvDest = static_cast<char*>(cudaBuffer) + yPlaneSize;
            CheckCudaError(cudaMemcpy2DAsync(
                uvDest, bufferPitch,
                m_hwFrame->data[1], m_hwFrame->linesize[1],
                m_width, m_height / 2,
                cudaMemcpyDeviceToDevice,
                m_cudaStream
            ));
        }

        // Synchronize to ensure all operations are complete
        CheckCudaError(cudaStreamSynchronize(m_cudaStream));

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error transferring frame to CUDA: " << e.what() << std::endl;
        return false;
    }
}

// ReadFrame implementation - returns the frame timestamp
double DirectVideoReader::ReadFrame(void* cudaBuffer, size_t bufferSize, size_t bufferPitch) {
    if (!m_isInitialized) return -1.0;

    // Decode a new frame
    if (!DecodePacket()) {
        return -1.0;
    }

    // Transfer frame data to CUDA memory
    if (!TransferFrameToCuda(cudaBuffer, bufferSize, bufferPitch)) {
        return -1.0;
    }

    // Return the frame timestamp in seconds
    return ConvertTimestampToSeconds(m_currentFrameTimestamp);
}

// Close implementation
void DirectVideoReader::Close() {
    // Release CUDA resources
    if (m_cudaFrameBuffer) {
        cudaFree(m_cudaFrameBuffer);
        m_cudaFrameBuffer = nullptr;
        m_cudaFrameBufferSize = 0;
    }

    if (m_yuv420pConversionBuffer) {
        cudaFree(m_yuv420pConversionBuffer);
        m_yuv420pConversionBuffer = nullptr;
        m_yuv420pConversionBufferSize = 0;
    }

    if (m_cudaStream) {
        cudaStreamDestroy(m_cudaStream);
        m_cudaStream = nullptr;
    }

    // Do not destroy the CUDA context, as it is owned by the caller.
    m_cudaContext = nullptr;

    // Release FFmpeg resources
    if (m_packet) {
        av_packet_free(&m_packet);
        m_packet = nullptr;
    }

    if (m_hwFrame) {
        av_frame_free(&m_hwFrame);
        m_hwFrame = nullptr;
    }

    if (m_codecContext) {
        avcodec_free_context(&m_codecContext);
        m_codecContext = nullptr;
    }

    if (m_formatContext) {
        avformat_close_input(&m_formatContext);
        m_formatContext = nullptr;
    }

    m_videoStreamIndex = -1;
    m_isHardwareAccelerated = false;
    m_width = 0;
    m_height = 0;
    m_duration = 0.0;
    m_frameRate = { 0, 1 };
    m_timeBase = { 0, 1 };
    m_textureFormat = VideoUtils::TextureFormat::Unknown;
    m_sourceFormat = VideoUtils::TextureFormat::Unknown;
    m_currentFrameTimestamp = 0;
    m_isInitialized = false;
    m_isEof = false;
}