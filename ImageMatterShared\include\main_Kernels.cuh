// main_Kernels.cuh - Updated header
#pragma once

#include <cuda_runtime.h>
#include "HeadDetector.h"

// Function declarations for CUDA kernel launchers
void launchByteNv12ToPlanarFloatRgb(unsigned char* nv12Data, float* rgbData, int width, int height, size_t nv12Pitch, cudaStream_t stream);
void launchRgbaToNv12(float* rgbaData, unsigned char* nv12Data, int width, int height, cudaStream_t stream);
void launchYuv420pToNv12(unsigned char* yuv420pData, unsigned char* nv12Data, int width, int height, int srcYStride, int srcUStride, int srcVStride, int dstStride, cudaStream_t stream);
void launchAlphaMattingToNv12(float* alphaMatte, unsigned char* nv12Data, int width, int height, size_t nv12Pitch, cudaStream_t stream);
void launchExtractAlphaFromNv12(unsigned char* nv12Data, float* alphaData, int width, int height, size_t nv12Pitch, cudaStream_t stream);
void launchRgbaToYuva444(float* rgbaData, unsigned char* yuvaData, int width, int height, cudaStream_t stream);
void launchCombineRgbAlphaToRgba(float* planarRgbData, float* alphaData, float* interleavedRgbaData, int width, int height, cudaStream_t stream);
void launchGenerateSyntheticAlpha(float* alphaData, int width, int height, int frameNumber, cudaStream_t stream);
void launchFloatToUint8Alpha(float* floatAlpha, unsigned char* uint8Alpha, int width, int height, cudaStream_t stream);
void launchUint8ToFloatAlpha(unsigned char* uint8Alpha, float* floatAlpha, int width, int height, cudaStream_t stream);
void launchDetectUncertainRegions(float* alphaBuffer, unsigned char* alphaRegionsBuffer, int width, int height, int regionSize, cudaStream_t stream);

// New function declarations for IndexNet processing
void launchPrepareIndexNetInput(float* outputBuffer, const float* inputRgbBuffer, const float* inputAlphaBuffer, int x, int y, int width, int height, int alphaRegionSize, int alphaRegionSizeY, int padding, cudaStream_t stream);

void launchUpdateAlphaBufferRegion(float* outputAlphaBuffer, cudaTextureObject_t originalAlphaTexture, const float* trimapBuffer, const float* improvedAlpha, int x, int y, int width, int height, int alphaRegionSize, int padding, cudaStream_t stream);

// Additional kernel functions for multi-pass processing
void launchExtractRegion(const float* inputBuffer, float* outputBuffer, int inputWidth, int inputHeight,
                        int regionX, int regionY, int regionWidth, int regionHeight, int channels, cudaStream_t stream);
void launchBlendAlphaRegion(float* mainAlpha, const float* regionAlpha, int mainWidth, int mainHeight,
                           int regionX, int regionY, int regionWidth, int regionHeight, cudaStream_t stream);
void launchResizeImage(const float* inputBuffer, float* outputBuffer, int inputWidth, int inputHeight,
                      int outputWidth, int outputHeight, int channels, cudaStream_t stream);

// Background estimation and foreground extraction functions
void launchEstimateBackgroundHorizontal(const float* alphaBuffer, const float* rgbBuffer, float* backgroundBuffer,
                                       int width, int height, cudaStream_t stream);
void launchEstimateBackgroundVertical(const float* alphaBuffer, const float* rgbBuffer, float* backgroundBuffer,
                                     int width, int height, cudaStream_t stream);
void launchExtractForeground(const float* alphaBuffer, const float* rgbBuffer, const float* horizontalBg,
                           const float* verticalBg, float* outputRgba, int width, int height, cudaStream_t stream);

void launchUpdateAlphaBufferHead(float* outputAlphaBuffer, const float* trimapBuffer, const float* improvedAlpha, int x, int y, int width, int height, int alphaRegionSizeC, int alphaRegionSizeY, cudaStream_t stream);

// New trimap generation functions
void launchGenerateTrimap(float* outputTrimap, const float* inputAlpha, int width, int height, cudaStream_t stream);
void launchExtendHeadUncertaintyTrimap(float* outputTrimap, const float* inputAlpha, int width, int height, int headX, int headY, int headWidth, int headHeight, cudaStream_t stream);

// Function to remove pixels under head boxes
void launchRemovePixelsUnderHeads(float* outputAlphaBuffer, const float* inputAlphaBuffer,
    const Box* headBoxes, int numHeads,
    int width, int height, cudaStream_t stream);

// Head region processing functions for FrameProcessorHeadOptimized
void launchExtractHeadRegion(
    const float* inputRgb, float* outputRgb,
    int extractX, int extractY, int extractWidth, int extractHeight,
    int inputWidth, int inputHeight,
    cudaStream_t stream);

void launchUpdateAlphaFromHeadRegion(
    float* mainAlphaBuffer,
    const float* headRegionAlpha,
    const float* trimap,
    int updateX, int updateY,
    int mainWidth, int mainHeight,
    int headRegionWidth, int headRegionHeight,
    cudaStream_t stream);

#ifdef __cplusplus
extern "C" {
#endif

void launchRescanHeadRegions(
    float* inputRgb,
    float* alphaMatte,
    Box* headBoxes,
    int numHeads,
    int width,
    int height,
    float bgThreshold,
    cudaStream_t stream
);

#ifdef __cplusplus
}
#endif
