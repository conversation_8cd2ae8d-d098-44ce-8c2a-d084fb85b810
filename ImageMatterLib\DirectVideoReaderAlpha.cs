using System;
using System.Runtime.InteropServices;
using System.Text;

namespace ImageMatterLib
{
    /// <summary>
    /// Engine type for video background removal
    /// </summary>
    public enum EngineType
    {
        ONNX = 0,
        TensorRT = 1,
        Auto = 2
    }

    /// <summary>
    /// Progress callback delegate for video processing
    /// </summary>
    /// <param name="currentFrame">Current frame index (0-based)</param>
    /// <param name="totalFrames">Total number of frames (if known, else 0)</param>
    /// <param name="userData">User data pointer</param>
    /// <returns>True to continue, false to cancel</returns>
    [UnmanagedFunctionPointer(CallingConvention.StdCall)]
    public delegate bool ProgressCallback(int currentFrame, int totalFrames, IntPtr userData);

    /// <summary>
    /// CPU-based ProRes 4444 video reader with alpha channel support
    /// </summary>
    public class DirectVideoReaderAlpha : IDisposable
    {
        private IntPtr _handle = IntPtr.Zero;
        private bool _disposed = false;

        #region P/Invoke Declarations

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Unicode)]
        private static extern IntPtr CreateVideoReaderAlpha([MarshalAs(UnmanagedType.LPWStr)] string filePath);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern void DestroyVideoReaderAlpha(IntPtr handle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int GetVideoWidth(IntPtr handle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int GetVideoHeight(IntPtr handle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern double GetVideoDuration(IntPtr handle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern double GetVideoFrameRate(IntPtr handle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        [return: MarshalAs(UnmanagedType.I1)]
        private static extern bool GetVideoHasAlpha(IntPtr handle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int GetVideoRgbaBufferSize(IntPtr handle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int GetVideoPixelFormatName(IntPtr handle, StringBuilder buffer, int bufferSize);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        [return: MarshalAs(UnmanagedType.I1)]
        private static extern bool SeekVideo(IntPtr handle, double timeInSeconds);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern double ReadVideoFrame(IntPtr handle, IntPtr cpuRgbaBuffer, int bufferSize);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Unicode)]
        private static extern int ConvertVideoToProRes4444(
            [MarshalAs(UnmanagedType.LPWStr)] string inputVideo,
            [MarshalAs(UnmanagedType.LPWStr)] string outputVideo,
            EngineType engine,
            ProgressCallback progressCb,
            IntPtr userData);

        #endregion

        /// <summary>
        /// Creates a new DirectVideoReaderAlpha instance
        /// </summary>
        /// <param name="filePath">Path to the video file</param>
        /// <exception cref="ArgumentException">Thrown when filePath is null or empty</exception>
        /// <exception cref="InvalidOperationException">Thrown when the video file cannot be opened</exception>
        public DirectVideoReaderAlpha(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

            _handle = CreateVideoReaderAlpha(filePath);
            if (_handle == IntPtr.Zero)
                throw new InvalidOperationException($"Failed to open video file: {filePath}");
        }

        /// <summary>
        /// Gets the width of the video in pixels
        /// </summary>
        public int Width
        {
            get
            {
                ThrowIfDisposed();
                return GetVideoWidth(_handle);
            }
        }

        /// <summary>
        /// Gets the height of the video in pixels
        /// </summary>
        public int Height
        {
            get
            {
                ThrowIfDisposed();
                return GetVideoHeight(_handle);
            }
        }

        /// <summary>
        /// Gets the duration of the video in seconds
        /// </summary>
        public double Duration
        {
            get
            {
                ThrowIfDisposed();
                return GetVideoDuration(_handle);
            }
        }

        /// <summary>
        /// Gets the frame rate of the video
        /// </summary>
        public double FrameRate
        {
            get
            {
                ThrowIfDisposed();
                return GetVideoFrameRate(_handle);
            }
        }

        /// <summary>
        /// Gets whether the video has an alpha channel
        /// </summary>
        public bool HasAlpha
        {
            get
            {
                ThrowIfDisposed();
                return GetVideoHasAlpha(_handle);
            }
        }

        /// <summary>
        /// Gets the required buffer size for RGBA frames in bytes
        /// </summary>
        public int RgbaBufferSize
        {
            get
            {
                ThrowIfDisposed();
                return GetVideoRgbaBufferSize(_handle);
            }
        }

        /// <summary>
        /// Gets the pixel format name of the video
        /// </summary>
        public string PixelFormatName
        {
            get
            {
                ThrowIfDisposed();
                var buffer = new StringBuilder(256);
                int length = GetVideoPixelFormatName(_handle, buffer, buffer.Capacity);
                return buffer.ToString();
            }
        }

        /// <summary>
        /// Seeks to a specific time position in the video
        /// </summary>
        /// <param name="timeInSeconds">Time position in seconds</param>
        /// <returns>True if seek was successful, false otherwise</returns>
        public bool Seek(double timeInSeconds)
        {
            ThrowIfDisposed();
            return SeekVideo(_handle, timeInSeconds);
        }

        /// <summary>
        /// Reads the next frame from the video into the provided buffer
        /// </summary>
        /// <param name="rgbaBuffer">Buffer to receive RGBA data (must be at least RgbaBufferSize bytes)</param>
        /// <returns>Frame timestamp in seconds, or negative value on error/EOF</returns>
        /// <exception cref="ArgumentNullException">Thrown when rgbaBuffer is null</exception>
        /// <exception cref="ArgumentException">Thrown when buffer is too small</exception>
        public double ReadFrame(byte[] rgbaBuffer)
        {
            ThrowIfDisposed();

            if (rgbaBuffer == null)
                throw new ArgumentNullException(nameof(rgbaBuffer));

            if (rgbaBuffer.Length < RgbaBufferSize)
                throw new ArgumentException($"Buffer too small. Required: {RgbaBufferSize}, provided: {rgbaBuffer.Length}");

            // Pin the managed array and get a pointer to it
            var handle = GCHandle.Alloc(rgbaBuffer, GCHandleType.Pinned);
            try
            {
                IntPtr bufferPtr = handle.AddrOfPinnedObject();
                return ReadVideoFrame(_handle, bufferPtr, rgbaBuffer.Length);
            }
            finally
            {
                handle.Free();
            }
        }

        /// <summary>
        /// Converts a video to ProRes 4444 format with background removal
        /// </summary>
        /// <param name="inputVideo">Input video file path</param>
        /// <param name="outputVideo">Output video file path</param>
        /// <param name="engine">AI engine to use</param>
        /// <param name="progressCallback">Optional progress callback</param>
        /// <returns>0 on success, non-zero on error</returns>
        public static int ConvertToProRes4444(string inputVideo, string outputVideo, EngineType engine = EngineType.Auto,
            ProgressCallback progressCallback = null)
        {
            return ConvertVideoToProRes4444(inputVideo, outputVideo, engine, progressCallback, IntPtr.Zero);
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(DirectVideoReaderAlpha));
        }

        #region IDisposable Implementation

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (_handle != IntPtr.Zero)
                {
                    DestroyVideoReaderAlpha(_handle);
                    _handle = IntPtr.Zero;
                }
                _disposed = true;
            }
        }

        ~DirectVideoReaderAlpha()
        {
            Dispose(false);
        }

        #endregion
    }
}
