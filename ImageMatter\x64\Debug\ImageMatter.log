﻿  alpha_codec.cpp
  AsyncDirectVideoReader.cpp
  CudaProResEncoder.cpp
  DetectHeadsStep.cpp
  DirectVideoReader.cpp
  DirectVideoReaderAlpha.cpp
  DirectVideoWriter.cpp
  DirectVideoWriterAlphaCuda.cpp
  FinalInitialAlphaStep.cpp
  FrameMetadataStorage.cpp
  FrameProcessor.cpp
  HeadDetector.cpp
  Helpers.cpp
  Helpers_Heads.cpp
  ImageMatting.cpp
  ImageMattingFactory.cpp
  ImageMattingOnnx.cpp
  ImageMattingTensorRt.cpp
  lodepng.cpp
  ProcessBodyRegionsStep.cpp
  Génération de code en cours...
  Compilation en cours...
  ProcessHeadRegionsStep.cpp
  ProcessingStep.cpp
  VideoBackgroundRemoval.cpp
F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterShared\VideoBackgroundRemoval.cpp(532,34): warning C4244: 'initialisation' : conversion de 'const _Ty' en 'int', perte possible de données
F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterShared\VideoBackgroundRemoval.cpp(532,34): warning C4244:         with
F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterShared\VideoBackgroundRemoval.cpp(532,34): warning C4244:         [
F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterShared\VideoBackgroundRemoval.cpp(532,34): warning C4244:             _Ty=float
F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterShared\VideoBackgroundRemoval.cpp(532,34): warning C4244:         ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=' : conversion de 'const wchar_t' en 'char', perte possible de données
  (compiler le fichier source 'ImageMatterShared/VideoBackgroundRemoval.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      le contexte d’instanciation du modèle (le plus ancien) est
          F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterShared\VideoBackgroundRemoval.cpp(400,33):
          voir la référence à l'instanciation de la fonction modèle 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_const_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' en cours de compilation
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterShared\VideoBackgroundRemoval.cpp(400,33):
              voir la première référence à « std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string » dans « ProcessMultiPass »
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          voir la référence à l'instanciation de la fonction modèle 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<const wchar_t*,const wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' en cours de compilation
          with
          [
              _Size_type=unsigned __int64,
              _Iter=const wchar_t *,
              _Sent=const wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          voir la référence à l'instanciation de la fonction modèle '_OutIt *std::_Copy_n_unchecked4<const wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' en cours de compilation
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=const wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  VideoWriterAlpha.cpp
  main_onephase.cpp
  Génération de code en cours...
     Création de la bibliothèque F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Debug\ImageMatter.lib et de l'objet F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Debug\ImageMatter.exp
alpha_codec.obj : error LNK2019: symbole externe non résolu launch_compress_columns référencé dans la fonction compress_alpha_cuda
alpha_codec.obj : error LNK2019: symbole externe non résolu launch_add_column_headers référencé dans la fonction compress_alpha_cuda
alpha_codec.obj : error LNK2019: symbole externe non résolu launch_compress_lines référencé dans la fonction compress_alpha_cuda
alpha_codec.obj : error LNK2019: symbole externe non résolu launch_decompress référencé dans la fonction decompress_alpha_cuda
CudaProResEncoder.obj : error LNK2019: symbole externe non résolu launchRgbaToYuva444_10bit référencé dans la fonction "public: bool __cdecl CudaProResEncoder::WriteFrame(void *,unsigned __int64)" (?WriteFrame@CudaProResEncoder@@QEAA_NPEAX_K@Z)
ProcessHeadRegionsStep.obj : error LNK2001: symbole externe non résolu "void __cdecl launchByteNv12ToPlanarFloatRgb(unsigned char *,float *,int,int,unsigned __int64,struct CUstream_st *)" (?launchByteNv12ToPlanarFloatRgb@@YAXPEAEPEAMHH_KPEAUCUstream_st@@@Z)
DetectHeadsStep.obj : error LNK2001: symbole externe non résolu "void __cdecl launchByteNv12ToPlanarFloatRgb(unsigned char *,float *,int,int,unsigned __int64,struct CUstream_st *)" (?launchByteNv12ToPlanarFloatRgb@@YAXPEAEPEAMHH_KPEAUCUstream_st@@@Z)
FinalInitialAlphaStep.obj : error LNK2001: symbole externe non résolu "void __cdecl launchByteNv12ToPlanarFloatRgb(unsigned char *,float *,int,int,unsigned __int64,struct CUstream_st *)" (?launchByteNv12ToPlanarFloatRgb@@YAXPEAEPEAMHH_KPEAUCUstream_st@@@Z)
FrameProcessor.obj : error LNK2001: symbole externe non résolu "void __cdecl launchByteNv12ToPlanarFloatRgb(unsigned char *,float *,int,int,unsigned __int64,struct CUstream_st *)" (?launchByteNv12ToPlanarFloatRgb@@YAXPEAEPEAMHH_KPEAUCUstream_st@@@Z)
ProcessBodyRegionsStep.obj : error LNK2001: symbole externe non résolu "void __cdecl launchByteNv12ToPlanarFloatRgb(unsigned char *,float *,int,int,unsigned __int64,struct CUstream_st *)" (?launchByteNv12ToPlanarFloatRgb@@YAXPEAEPEAMHH_KPEAUCUstream_st@@@Z)
DirectVideoReader.obj : error LNK2019: symbole externe non résolu "void __cdecl launchYuv420pToNv12(unsigned char *,unsigned char *,int,int,int,int,int,int,struct CUstream_st *)" (?launchYuv420pToNv12@@YAXPEAE0HHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: bool __cdecl DirectVideoReader::TransferFrameToCuda(void *,unsigned __int64,unsigned __int64)" (?TransferFrameToCuda@DirectVideoReader@@AEAA_NPEAX_K1@Z)
FrameProcessor.obj : error LNK2019: symbole externe non résolu launchEstimateBackgroundHorizontal référencé dans la fonction "public: bool __cdecl FrameProcessor::ProcessFrame(void *,unsigned __int64,float *)" (?ProcessFrame@FrameProcessor@@QEAA_NPEAX_KPEAM@Z)
FrameProcessor.obj : error LNK2019: symbole externe non résolu launchEstimateBackgroundVertical référencé dans la fonction "public: bool __cdecl FrameProcessor::ProcessFrame(void *,unsigned __int64,float *)" (?ProcessFrame@FrameProcessor@@QEAA_NPEAX_KPEAM@Z)
FrameProcessor.obj : error LNK2019: symbole externe non résolu launchExtractForeground référencé dans la fonction "public: bool __cdecl FrameProcessor::ProcessFrame(void *,unsigned __int64,float *)" (?ProcessFrame@FrameProcessor@@QEAA_NPEAX_KPEAM@Z)
FrameProcessor.obj : error LNK2019: symbole externe non résolu "void __cdecl launchFloatToUint8Alpha(float *,unsigned char *,int,int,struct CUstream_st *)" (?launchFloatToUint8Alpha@@YAXPEAMPEAEHHPEAUCUstream_st@@@Z) référencé dans la fonction "public: bool __cdecl FrameProcessor::FindInitialAlpha(void *,unsigned __int64,unsigned char * *,unsigned __int64 *)" (?FindInitialAlpha@FrameProcessor@@QEAA_NPEAX_KPEAPEAEPEA_K@Z)
FrameProcessor.obj : error LNK2019: symbole externe non résolu "void __cdecl launchUint8ToFloatAlpha(unsigned char *,float *,int,int,struct CUstream_st *)" (?launchUint8ToFloatAlpha@@YAXPEAEPEAMHHPEAUCUstream_st@@@Z) référencé dans la fonction "public: bool __cdecl FrameProcessor::RefineInitialAlpha(void *,unsigned __int64,unsigned char *,unsigned __int64,float *)" (?RefineInitialAlpha@FrameProcessor@@QEAA_NPEAX_KPEAE1PEAM@Z)
FrameProcessor.obj : error LNK2019: symbole externe non résolu "void __cdecl launchDetectUncertainRegions(float *,unsigned char *,int,int,int,struct CUstream_st *)" (?launchDetectUncertainRegions@@YAXPEAMPEAEHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl FrameProcessor::ProcessBodyRegions(void)" (?ProcessBodyRegions@FrameProcessor@@AEAAXXZ)
ProcessBodyRegionsStep.obj : error LNK2001: symbole externe non résolu "void __cdecl launchDetectUncertainRegions(float *,unsigned char *,int,int,int,struct CUstream_st *)" (?launchDetectUncertainRegions@@YAXPEAMPEAEHHHPEAUCUstream_st@@@Z)
FrameProcessor.obj : error LNK2019: symbole externe non résolu "void __cdecl launchPrepareIndexNetInput(float *,float const *,float const *,int,int,int,int,int,int,int,struct CUstream_st *)" (?launchPrepareIndexNetInput@@YAXPEAMPEBM1HHHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl FrameProcessor::ProcessBodyRegions(void)" (?ProcessBodyRegions@FrameProcessor@@AEAAXXZ)
FrameProcessor.obj : error LNK2019: symbole externe non résolu "void __cdecl launchUpdateAlphaBufferRegion(float *,unsigned __int64,float const *,float const *,int,int,int,int,int,int,struct CUstream_st *)" (?launchUpdateAlphaBufferRegion@@YAXPEAM_KPEBM2HHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl FrameProcessor::ProcessBodyRegions(void)" (?ProcessBodyRegions@FrameProcessor@@AEAAXXZ)
FrameProcessor.obj : error LNK2019: symbole externe non résolu "void __cdecl launchGenerateTrimap(float *,float const *,int,int,struct CUstream_st *)" (?launchGenerateTrimap@@YAXPEAMPEBMHHPEAUCUstream_st@@@Z) référencé dans la fonction "public: bool __cdecl FrameProcessor::ProcessFrame(void *,unsigned __int64,float *)" (?ProcessFrame@FrameProcessor@@QEAA_NPEAX_KPEAM@Z)
ProcessBodyRegionsStep.obj : error LNK2001: symbole externe non résolu "void __cdecl launchGenerateTrimap(float *,float const *,int,int,struct CUstream_st *)" (?launchGenerateTrimap@@YAXPEAMPEBMHHPEAUCUstream_st@@@Z)
ProcessHeadRegionsStep.obj : error LNK2001: symbole externe non résolu "void __cdecl launchGenerateTrimap(float *,float const *,int,int,struct CUstream_st *)" (?launchGenerateTrimap@@YAXPEAMPEBMHHPEAUCUstream_st@@@Z)
FrameProcessor.obj : error LNK2019: symbole externe non résolu "void __cdecl launchExtractHeadRegion(float const *,float *,int,int,int,int,int,int,struct CUstream_st *)" (?launchExtractHeadRegion@@YAXPEBMPEAMHHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl FrameProcessor::ProcessHeadRegions(void)" (?ProcessHeadRegions@FrameProcessor@@AEAAXXZ)
FrameProcessor.obj : error LNK2019: symbole externe non résolu "void __cdecl launchUpdateAlphaFromHeadRegion(float *,float const *,float const *,int,int,int,int,int,int,struct CUstream_st *)" (?launchUpdateAlphaFromHeadRegion@@YAXPEAMPEBM1HHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl FrameProcessor::ProcessHeadRegions(void)" (?ProcessHeadRegions@FrameProcessor@@AEAAXXZ)
FrameProcessor.obj : error LNK2019: symbole externe non résolu launchRescanHeadRegions référencé dans la fonction "public: bool __cdecl FrameProcessor::ProcessFrame(void *,unsigned __int64,float *)" (?ProcessFrame@FrameProcessor@@QEAA_NPEAX_KPEAM@Z)
HeadDetector.obj : error LNK2019: symbole externe non résolu LaunchLanczosResizeAndPadKernel référencé dans la fonction "public: bool __cdecl HeadDetector::DetectHeads(float *,int,int,class std::vector<struct Box,class std::allocator<struct Box> > &,float)" (?DetectHeads@HeadDetector@@QEAA_NPEAMHHAEAV?$vector@UBox@@V?$allocator@UBox@@@std@@@std@@M@Z)
ImageMatting.obj : error LNK2001: symbole externe non résolu LaunchLanczosResizeAndPadKernel
Helpers_Heads.obj : error LNK2019: symbole externe non résolu "bool __cdecl LaunchDrawRectangleKernel(float *,int,int,int,int,int,int,float,float,float,int,struct CUstream_st *)" (?LaunchDrawRectangleKernel@@YA_NPEAMHHHHHHMMMHPEAUCUstream_st@@@Z) référencé dans la fonction "bool __cdecl DrawHeadDetectionDebugOverlay(float *,int,int,class std::vector<struct Box,class std::allocator<struct Box> > const &)" (?DrawHeadDetectionDebugOverlay@@YA_NPEAMHHAEBV?$vector@UBox@@V?$allocator@UBox@@@std@@@std@@@Z)
Helpers_Heads.obj : error LNK2019: symbole externe non résolu "bool __cdecl LaunchDrawNumberKernel(float *,int,int,int,int,int,float,float,float,struct CUstream_st *)" (?LaunchDrawNumberKernel@@YA_NPEAMHHHHHMMMPEAUCUstream_st@@@Z) référencé dans la fonction "bool __cdecl DrawHeadDetectionDebugOverlay(float *,int,int,class std::vector<struct Box,class std::allocator<struct Box> > const &)" (?DrawHeadDetectionDebugOverlay@@YA_NPEAMHHAEBV?$vector@UBox@@V?$allocator@UBox@@@std@@@std@@@Z)
ImageMatting.obj : error LNK2019: symbole externe non résolu "enum cudaError __cdecl LaunchPreprocessBufferKernel(float *,float const *,int,int,bool,struct NormalizationParams const &,struct CUstream_st *)" (?LaunchPreprocessBufferKernel@@YA?AW4cudaError@@PEAMPEBMHH_NAEBUNormalizationParams@@PEAUCUstream_st@@@Z) référencé dans la fonction "protected: enum cudaError __cdecl ImageMatting::PreprocessInputBufferCommon(float const *,float *,float *,int,int,int,int,bool,struct NormalizationParams const &,unsigned __int64,enum ResizeMethod,struct CUstream_st *)" (?PreprocessInputBufferCommon@ImageMatting@@IEAA?AW4cudaError@@PEBMPEAM1HHHH_NAEBUNormalizationParams@@_KW4ResizeMethod@@PEAUCUstream_st@@@Z)
ImageMatting.obj : error LNK2019: symbole externe non résolu LanczosResizeKernelLauncher référencé dans la fonction "protected: enum cudaError __cdecl ImageMatting::PreprocessInputBufferCommon(float const *,float *,float *,int,int,int,int,bool,struct NormalizationParams const &,unsigned __int64,enum ResizeMethod,struct CUstream_st *)" (?PreprocessInputBufferCommon@ImageMatting@@IEAA?AW4cudaError@@PEBMPEAM1HHHH_NAEBUNormalizationParams@@_KW4ResizeMethod@@PEAUCUstream_st@@@Z)
ImageMatting.obj : error LNK2019: symbole externe non résolu LanczosUpscaleKernelLauncher référencé dans la fonction "protected: enum cudaError __cdecl ImageMatting::PostprocessOutputBufferCommon(float const *,float *,int,int,int,int,enum ResizeMethod,struct CUstream_st *)" (?PostprocessOutputBufferCommon@ImageMatting@@IEAA?AW4cudaError@@PEBMPEAMHHHHW4ResizeMethod@@PEAUCUstream_st@@@Z)
ProcessBodyRegionsStep.obj : error LNK2019: symbole externe non résolu "void __cdecl launchEstimateBackgroundHorizontal(float const *,float const *,float *,int,int,struct CUstream_st *)" (?launchEstimateBackgroundHorizontal@@YAXPEBM0PEAMHHPEAUCUstream_st@@@Z) référencé dans la fonction "public: bool __cdecl ProcessBodyRegionsStep::GenerateFinalOutput(void *,unsigned __int64,struct FrameMetadata const &,float *)" (?GenerateFinalOutput@ProcessBodyRegionsStep@@QEAA_NPEAX_KAEBUFrameMetadata@@PEAM@Z)
ProcessBodyRegionsStep.obj : error LNK2019: symbole externe non résolu "void __cdecl launchEstimateBackgroundVertical(float const *,float const *,float *,int,int,struct CUstream_st *)" (?launchEstimateBackgroundVertical@@YAXPEBM0PEAMHHPEAUCUstream_st@@@Z) référencé dans la fonction "public: bool __cdecl ProcessBodyRegionsStep::GenerateFinalOutput(void *,unsigned __int64,struct FrameMetadata const &,float *)" (?GenerateFinalOutput@ProcessBodyRegionsStep@@QEAA_NPEAX_KAEBUFrameMetadata@@PEAM@Z)
ProcessBodyRegionsStep.obj : error LNK2019: symbole externe non résolu "void __cdecl launchExtractForeground(float const *,float const *,float const *,float const *,float *,int,int,struct CUstream_st *)" (?launchExtractForeground@@YAXPEBM000PEAMHHPEAUCUstream_st@@@Z) référencé dans la fonction "public: bool __cdecl ProcessBodyRegionsStep::GenerateFinalOutput(void *,unsigned __int64,struct FrameMetadata const &,float *)" (?GenerateFinalOutput@ProcessBodyRegionsStep@@QEAA_NPEAX_KAEBUFrameMetadata@@PEAM@Z)
ProcessHeadRegionsStep.obj : error LNK2019: symbole externe non résolu "void __cdecl launchExtractRegion(float const *,float *,int,int,int,int,int,int,int,struct CUstream_st *)" (?launchExtractRegion@@YAXPEBMPEAMHHHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl ProcessHeadRegionsStep::ProcessHeadRegionsImpl(void)" (?ProcessHeadRegionsImpl@ProcessHeadRegionsStep@@AEAAXXZ)
ProcessHeadRegionsStep.obj : error LNK2019: symbole externe non résolu "void __cdecl launchBlendAlphaRegion(float *,float const *,int,int,int,int,int,int,struct CUstream_st *)" (?launchBlendAlphaRegion@@YAXPEAMPEBMHHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl ProcessHeadRegionsStep::ProcessHeadRegionsImpl(void)" (?ProcessHeadRegionsImpl@ProcessHeadRegionsStep@@AEAAXXZ)
ProcessHeadRegionsStep.obj : error LNK2019: symbole externe non résolu "void __cdecl launchResizeImage(float const *,float *,int,int,int,int,int,struct CUstream_st *)" (?launchResizeImage@@YAXPEBMPEAMHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl ProcessHeadRegionsStep::ProcessHeadRegionsImpl(void)" (?ProcessHeadRegionsImpl@ProcessHeadRegionsStep@@AEAAXXZ)
VideoWriterAlpha.obj : error LNK2019: symbole externe non résolu "void __cdecl launchRgbaToYuva444(float *,unsigned char *,int,int,struct CUstream_st *)" (?launchRgbaToYuva444@@YAXPEAMPEAEHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: bool __cdecl VideoWriterAlpha::TransferRgbaToFrame(void *,unsigned __int64)" (?TransferRgbaToFrame@VideoWriterAlpha@@AEAA_NPEAX_K@Z)
F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Debug\ImageMatter.exe : fatal error LNK1120: 32 externes non résolus
