// DirectVideoWriterAlphaCuda.h
#pragma once

#include <string>
#include <memory>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <memory>
#include <cuda.h>
#include <cuda_runtime.h>

extern "C" {
#include <libavutil/rational.h>
}

class CudaProResEncoder;

class DirectVideoWriterAlphaCuda {
public:
    // Configuration structure for output video
    struct OutputConfig {
        int width = 1920;
        int height = 1080;
        AVRational frameRate = {30, 1}; // 30 fps default
        std::string outputPath = "output.mov";
        int quality = 3; // ProRes 4444
        bool enableAlpha = true;

        // Preset configurations
        void UseProRes4444() {
            quality = 3;
            enableAlpha = true;
        }

        void UseProRes4444XQ() {
            quality = 4;
            enableAlpha = true;
        }
    };

    // Structure for frame data in encoding queue with optimized memory operations
    struct FrameData {
        void* cudaRgbaBuffer;  // Owned copy of frame data
        size_t bufferSize;
        int64_t pts;
        bool isLastFrame;
        cudaEvent_t copyCompleteEvent;  // Event to track async copy completion

        FrameData(void* buffer, size_t size, int64_t timestamp, cudaStream_t stream, bool last = false)
            : cudaRgbaBuffer(nullptr), bufferSize(size), pts(timestamp), isLastFrame(last), copyCompleteEvent(nullptr) {
            if (buffer && size > 0 && !last) {
                // Allocate buffer for frame data
                cudaMalloc(&cudaRgbaBuffer, size);

                // Create event to track copy completion
                cudaEventCreate(&copyCompleteEvent);

                // Perform asynchronous copy to avoid blocking
                cudaMemcpyAsync(cudaRgbaBuffer, buffer, size, cudaMemcpyDeviceToDevice, stream);

                // Record event after copy
                cudaEventRecord(copyCompleteEvent, stream);
            } else {
                cudaRgbaBuffer = nullptr;
            }
        }

        // Wait for copy to complete before using the buffer
        void WaitForCopyComplete() {
            if (copyCompleteEvent) {
                cudaEventSynchronize(copyCompleteEvent);
            }
        }

        // Destructor to free allocated memory
        ~FrameData() {
            if (cudaRgbaBuffer) {
                cudaFree(cudaRgbaBuffer);
            }
            if (copyCompleteEvent) {
                cudaEventDestroy(copyCompleteEvent);
            }
        }

        // Move constructor for queue operations
        FrameData(FrameData&& other) noexcept
            : cudaRgbaBuffer(other.cudaRgbaBuffer), bufferSize(other.bufferSize),
              pts(other.pts), isLastFrame(other.isLastFrame) {
            other.cudaRgbaBuffer = nullptr; // Transfer ownership
        }

        // Move assignment operator
        FrameData& operator=(FrameData&& other) noexcept {
            if (this != &other) {
                if (cudaRgbaBuffer) cudaFree(cudaRgbaBuffer);
                cudaRgbaBuffer = other.cudaRgbaBuffer;
                bufferSize = other.bufferSize;
                pts = other.pts;
                isLastFrame = other.isLastFrame;
                other.cudaRgbaBuffer = nullptr;
            }
            return *this;
        }

        // Delete copy constructor and assignment to prevent accidental copying
        FrameData(const FrameData&) = delete;
        FrameData& operator=(const FrameData&) = delete;
    };

    // Factory method to create a DirectVideoWriterAlphaCuda instance
    static std::unique_ptr<DirectVideoWriterAlphaCuda> Create(const OutputConfig& config, CUcontext cudaContext);

    // Destructor
    ~DirectVideoWriterAlphaCuda();

    // Write a frame from CUDA memory (RGBA format) asynchronously
    bool WriteFrame(void* cudaRgbaBuffer, size_t bufferSize);

    // Finalize the video file (waits for all pending operations to complete)
    bool Finalize();

    // Get encoding queue size for monitoring
    size_t GetQueueSize() const;

    // Check if there are encoding errors
    bool HasEncodingError() const { return m_encodingError.load(); }

    // Get statistics
    size_t GetFrameCount() const;
    size_t GetTotalBytes() const;

private:
    // Private constructor - use Create() method
    DirectVideoWriterAlphaCuda();

    // Initialize the writer
    bool Initialize(const OutputConfig& config, CUcontext cudaContext);

    // Asynchronous encoding thread
    void EncodingThreadFunction();

    // Member variables
    OutputConfig m_config;
    CUcontext m_cudaContext;

    // CUDA ProRes encoder
    std::unique_ptr<CudaProResEncoder> m_encoder;

    // Video properties
    int64_t m_pts;

    // State flags
    bool m_isInitialized;
    bool m_isFinalized;

    // Asynchronous encoding with optimized performance
    std::thread m_encodingThread;
    std::queue<std::unique_ptr<FrameData>> m_frameQueue;
    mutable std::mutex m_queueMutex;
    std::condition_variable m_queueCondition;
    std::condition_variable m_spaceAvailableCondition;  // For efficient blocking when queue is full
    std::atomic<bool> m_stopEncoding{false};
    std::atomic<bool> m_encodingError{false};
    static const size_t MAX_QUEUE_SIZE = 8;  // Increased for better throughput

    // Dedicated CUDA stream for asynchronous encoding operations
    cudaStream_t m_encodingStream;
};
