#include "ProcessHeadRegionsStep.h"
#include "ImageMattingFactory.h"
#include "Matting_Kernels.cuh"
#include "main_Kernels.cuh"
#include "alpha_codec.h"
#include "Helpers.h"
#include <iostream>
#include <cuda_runtime.h>
#include <algorithm>

ProcessHeadRegionsStep::ProcessHeadRegionsStep(int headModelSize)
    : m_rgb<PERSON>uffer(nullptr)
    , m_rgbBufferSize(0)
    , m_decompressed<PERSON>lpha(nullptr)
    , m_trimapBuffer(nullptr)
    , m_headRgbBuffer(nullptr)
    , m_headAlphaBuffer(nullptr)
    , m_width(0)
    , m_height(0)
    , m_headModelSize(headModelSize)
    , m_cudaContext(nullptr)
    , m_stream(nullptr)
    , m_initialized(false)
    , m_inspyreNet(nullptr) {

    // Generate step name with model size
    m_stepName = "ProcessHeadRegions_" + std::to_string(headModelSize);
}

ProcessHeadRegionsStep::~ProcessHeadRegionsStep() {
    DeallocateBuffers();
}

const char* ProcessHeadRegionsStep::GetStepName() const {
    return m_stepName.c_str();
}

bool ProcessHeadRegionsStep::Initialize(int width, int height, CUcontext cudaContext, cudaStream_t stream) {
    if (m_initialized) {
        std::cerr << "ProcessHeadRegionsStep already initialized" << std::endl;
        return false;
    }

    m_width = width;
    m_height = height;
    m_cudaContext = cudaContext;
    m_stream = stream;

    // Set CUDA context
    CUresult contextResult = cuCtxSetCurrent(m_cudaContext);
    if (contextResult != CUDA_SUCCESS) {
        std::cerr << "Failed to set CUDA context in ProcessHeadRegionsStep: " << contextResult << std::endl;
        return false;
    }

    // Allocate processing buffers
    if (!AllocateBuffers()) {
        std::cerr << "Failed to allocate buffers for ProcessHeadRegionsStep" << std::endl;
        return false;
    }

    // Initialize InsPyReNet model for the specified head size
    m_inspyreNet = ImageMattingFactory::Init(ModelType::INSPYRENET, m_headModelSize, m_headModelSize, stream);
    if (!m_inspyreNet) {
        std::cerr << "Failed to create InsPyReNet model for size " << m_headModelSize << std::endl;
        DeallocateBuffers();
        return false;
    }

    m_initialized = true;
    std::cout << "ProcessHeadRegionsStep initialized successfully (" << width << "x" << height << ") with model size " << m_headModelSize << std::endl;
    return true;
}

bool ProcessHeadRegionsStep::Process(void* inputNv12Data, size_t inputPitch, FrameMetadata& frameMetadata) {
    if (!m_initialized) {
        std::cerr << "ProcessHeadRegionsStep not initialized" << std::endl;
        return false;
    }

    if (!frameMetadata.hasInitialAlpha) {
        std::cerr << "ProcessHeadRegionsStep requires initial alpha data" << std::endl;
        return false;
    }

    if (!frameMetadata.hasHeadDetection) {
        std::cerr << "ProcessHeadRegionsStep requires head detection data" << std::endl;
        return false;
    }

    // Set CUDA context for this thread
    CUresult contextResult = cuCtxSetCurrent(m_cudaContext);
    if (contextResult != CUDA_SUCCESS) {
        std::cerr << "Failed to set CUDA context in ProcessHeadRegionsStep::Process: " << contextResult << std::endl;
        return false;
    }

    try {
        // Convert NV12 to RGB for processing
        launchByteNv12ToPlanarFloatRgb(
            static_cast<unsigned char*>(inputNv12Data),
            m_rgbBuffer,
            m_width,
            m_height,
            inputPitch,
            m_stream
        );

        cudaError_t result = cudaStreamSynchronize(m_stream);
        if (result != cudaSuccess) {
            std::cerr << "Failed to synchronize CUDA stream: " << cudaGetErrorString(result) << std::endl;
            return false;
        }

        // Decompress alpha data from frame metadata
        if (!DecompressAlpha(frameMetadata)) {
            std::cerr << "Failed to decompress alpha data" << std::endl;
            return false;
        }

        // Filter head detections to only process heads that match this model size
        std::vector<Box> headsToProcess;
        for (int headIndex = 0; headIndex < frameMetadata.numHeadDetections; ++headIndex) {
            const Box& head = frameMetadata.headDetections[headIndex];

            // Calculate head size (max of width/height)
            int headWidth = static_cast<int>(head.width);
            int headHeight = static_cast<int>(head.height);
            int headSize = std::max(headWidth, headHeight);

            // Determine appropriate InsPyReNet model size
            int requiredModelSize;
            if (headSize <= 320) {
                requiredModelSize = 320;
            } else if (headSize <= 640) {
                requiredModelSize = 640;
            } else {
                requiredModelSize = 1024;
            }

            // Only process heads that match this step's model size
            if (requiredModelSize == m_headModelSize) {
                headsToProcess.push_back(head);
            }
        }

        // If no heads match this model size, skip processing
        if (headsToProcess.empty()) {
            return true; // Success, but nothing to process
        }

        // Generate trimap from alpha
        launchGenerateTrimap(m_trimapBuffer, m_decompressedAlpha, m_width, m_height, m_stream);
        cudaError_t syncResult = cudaStreamSynchronize(m_stream);
        if (syncResult != cudaSuccess) {
            std::cerr << "Failed to synchronize CUDA stream: " << cudaGetErrorString(syncResult) << std::endl;
            return false;
        }

        // Store filtered heads for processing
        m_headDetections = headsToProcess;

        // Process head regions using InsPyReNet
        ProcessHeadRegionsImpl();

        // Mark this step as completed
        frameMetadata.hasHeadProcessing = true;
        frameMetadata.processingStats.numHeadRegionsProcessed = static_cast<int>(headsToProcess.size());

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in ProcessHeadRegionsStep::Process: " << e.what() << std::endl;
        return false;
    }
}

bool ProcessHeadRegionsStep::AllocateBuffers() {
    try {
        // Allocate RGB buffer
        m_rgbBufferSize = m_width * m_height * 3 * sizeof(float);
        cudaError_t result = cudaMalloc(&m_rgbBuffer, m_rgbBufferSize);
        if (result != cudaSuccess) {
            std::cerr << "Failed to allocate RGB buffer: " << cudaGetErrorString(result) << std::endl;
            return false;
        }

        // Allocate alpha processing buffers
        size_t alphaBufferSize = m_width * m_height * sizeof(float);
        result = cudaMalloc(&m_decompressedAlpha, alphaBufferSize);
        if (result != cudaSuccess) {
            std::cerr << "Failed to allocate decompressed alpha buffer: " << cudaGetErrorString(result) << std::endl;
            return false;
        }

        result = cudaMalloc(&m_trimapBuffer, alphaBufferSize);
        if (result != cudaSuccess) {
            std::cerr << "Failed to allocate trimap buffer: " << cudaGetErrorString(result) << std::endl;
            return false;
        }

        // Allocate head-specific buffers for InsPyReNet processing
        size_t headBufferSize = m_headModelSize * m_headModelSize * 3 * sizeof(float);
        result = cudaMalloc(&m_headRgbBuffer, headBufferSize);
        if (result != cudaSuccess) {
            std::cerr << "Failed to allocate head RGB buffer: " << cudaGetErrorString(result) << std::endl;
            return false;
        }

        size_t headAlphaBufferSize = m_headModelSize * m_headModelSize * sizeof(float);
        result = cudaMalloc(&m_headAlphaBuffer, headAlphaBufferSize);
        if (result != cudaSuccess) {
            std::cerr << "Failed to allocate head alpha buffer: " << cudaGetErrorString(result) << std::endl;
            return false;
        }

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Failed to allocate buffers: " << e.what() << std::endl;
        DeallocateBuffers();
        return false;
    }
}

void ProcessHeadRegionsStep::DeallocateBuffers() {
    if (m_rgbBuffer) {
        cudaFree(m_rgbBuffer);
        m_rgbBuffer = nullptr;
    }

    if (m_decompressedAlpha) {
        cudaFree(m_decompressedAlpha);
        m_decompressedAlpha = nullptr;
    }

    if (m_trimapBuffer) {
        cudaFree(m_trimapBuffer);
        m_trimapBuffer = nullptr;
    }

    if (m_headRgbBuffer) {
        cudaFree(m_headRgbBuffer);
        m_headRgbBuffer = nullptr;
    }

    if (m_headAlphaBuffer) {
        cudaFree(m_headAlphaBuffer);
        m_headAlphaBuffer = nullptr;
    }

    m_rgbBufferSize = 0;
}

bool ProcessHeadRegionsStep::DecompressAlpha(const FrameMetadata& frameMetadata) {
    if (!frameMetadata.hasInitialAlpha || frameMetadata.encodedAlphaSize == 0) {
        return false;
    }

    // For now, assume simple byte-to-float conversion (matching FinalInitialAlphaStep encoding)
    size_t alphaPixelCount = m_width * m_height;

    if (frameMetadata.encodedAlphaSize != alphaPixelCount) {
        std::cerr << "Encoded alpha size mismatch: expected " << alphaPixelCount
                  << ", got " << frameMetadata.encodedAlphaSize << std::endl;
        return false;
    }

    // Allocate host buffer for conversion
    float* hostAlphaBuffer = new float[alphaPixelCount];

    // Convert bytes back to floats
    for (size_t i = 0; i < alphaPixelCount; ++i) {
        hostAlphaBuffer[i] = frameMetadata.encodedAlpha[i] / 255.0f;
    }

    // Copy to GPU
    cudaError_t result = cudaMemcpy(m_decompressedAlpha, hostAlphaBuffer,
                                   alphaPixelCount * sizeof(float),
                                   cudaMemcpyHostToDevice);

    delete[] hostAlphaBuffer;

    if (result != cudaSuccess) {
        std::cerr << "Failed to copy decompressed alpha to GPU: " << cudaGetErrorString(result) << std::endl;
        return false;
    }

    return true;
}

void ProcessHeadRegionsStep::ProcessHeadRegionsImpl() {
    // Full implementation of head region processing
    // Process each detected head region with InsPyReNet for better hair detail

    std::cout << "ProcessHeadRegionsStep: Processing " << m_headDetections.size() << " head regions" << std::endl;

    if (m_headDetections.empty() || !m_inspyreNet) {
        return;
    }

    // Process each detected head region
    for (const Box& head : m_headDetections) {
        // Calculate head region bounds with padding
        int headWidth = static_cast<int>(head.width);
        int headHeight = static_cast<int>(head.height);
        int headX = static_cast<int>(head.x - head.width / 2);
        int headY = static_cast<int>(head.y - head.height / 2);

        // Add padding around head region
        int padding = std::max(headWidth, headHeight) / 4;
        headX = std::max(0, headX - padding);
        headY = std::max(0, headY - padding);
        headWidth = std::min(m_width - headX, headWidth + 2 * padding);
        headHeight = std::min(m_height - headY, headHeight + 2 * padding);

        // Skip if region is too small or invalid
        if (headWidth < 32 || headHeight < 32) {
            continue;
        }

        // Extract head region from RGB and alpha data
        float* d_headRgb = nullptr;
        float* d_headAlpha = nullptr;
        cudaError_t result = cudaMalloc(&d_headRgb, headWidth * headHeight * 3 * sizeof(float));
        if (result != cudaSuccess) {
            std::cerr << "Failed to allocate head RGB buffer: " << cudaGetErrorString(result) << std::endl;
            continue;
        }
        result = cudaMalloc(&d_headAlpha, headWidth * headHeight * sizeof(float));
        if (result != cudaSuccess) {
            std::cerr << "Failed to allocate head alpha buffer: " << cudaGetErrorString(result) << std::endl;
            cudaFree(d_headRgb);
            continue;
        }

        // Extract RGB region
        launchExtractRegion(m_rgbPlanar, d_headRgb, m_width, m_height, headX, headY, headWidth, headHeight, 3, m_stream);

        // Extract alpha region
        launchExtractRegion(m_decompressedAlpha, d_headAlpha, m_width, m_height, headX, headY, headWidth, headHeight, 1, m_stream);

        // Resize to model input size if needed
        if (headWidth != m_headModelSize || headHeight != m_headModelSize) {
            float* d_resizedRgb = nullptr;
            result = cudaMalloc(&d_resizedRgb, m_headModelSize * m_headModelSize * 3 * sizeof(float));
            if (result != cudaSuccess) {
                std::cerr << "Failed to allocate resized RGB buffer: " << cudaGetErrorString(result) << std::endl;
                cudaFree(d_headRgb);
                cudaFree(d_headAlpha);
                continue;
            }

            // Resize RGB to model input size
            launchResizeImage(d_headRgb, d_resizedRgb, headWidth, headHeight, m_headModelSize, m_headModelSize, 3, m_stream);

            // Copy resized data to model input
            result = cudaMemcpy(m_inspyreNet->GetInputBuffer(), d_resizedRgb,
                                m_headModelSize * m_headModelSize * 3 * sizeof(float), cudaMemcpyDeviceToDevice);
            if (result != cudaSuccess) {
                std::cerr << "Failed to copy resized data to model input: " << cudaGetErrorString(result) << std::endl;
                cudaFree(d_resizedRgb);
                cudaFree(d_headRgb);
                cudaFree(d_headAlpha);
                continue;
            }

            cudaFree(d_resizedRgb);
        } else {
            // Copy directly to model input
            result = cudaMemcpy(m_inspyreNet->GetInputBuffer(), d_headRgb,
                                headWidth * headHeight * 3 * sizeof(float), cudaMemcpyDeviceToDevice);
            if (result != cudaSuccess) {
                std::cerr << "Failed to copy head data to model input: " << cudaGetErrorString(result) << std::endl;
                cudaFree(d_headRgb);
                cudaFree(d_headAlpha);
                continue;
            }
        }

        // Run InsPyReNet inference for refined alpha
        if (!m_inspyreNet->Infer()) {
            std::cerr << "Failed to run InsPyReNet inference for head region" << std::endl;
            cudaFree(d_headRgb);
            cudaFree(d_headAlpha);
            continue;
        }

        // Get refined alpha from model output
        float* d_refinedAlpha = m_inspyreNet->GetOutputBuffer();

        // Resize refined alpha back to original head region size if needed
        if (headWidth != m_headModelSize || headHeight != m_headModelSize) {
            float* d_resizedAlpha = nullptr;
            cudaError_t result = cudaMalloc(&d_resizedAlpha, headWidth * headHeight * sizeof(float));
            if (result != cudaSuccess) {
                std::cerr << "Failed to allocate resized alpha buffer: " << cudaGetErrorString(result) << std::endl;
                continue;
            }

            launchResizeImage(d_refinedAlpha, d_resizedAlpha, m_headModelSize, m_headModelSize, headWidth, headHeight, 1, m_stream);

            // Blend refined alpha back into main alpha buffer
            launchBlendAlphaRegion(m_originalAlphaTexture, d_resizedAlpha, m_width, m_height, headX, headY, headWidth, headHeight, m_stream);

            cudaFree(d_resizedAlpha);
        } else {
            // Blend refined alpha directly back into main alpha buffer
            launchBlendAlphaRegion(m_originalAlphaTexture, d_refinedAlpha, m_width, m_height, headX, headY, headWidth, headHeight, m_stream);
        }

        // Clean up region buffers
        cudaFree(d_headRgb);
        cudaFree(d_headAlpha);
    }

    cudaError_t result = cudaStreamSynchronize(m_stream);
    if (result != cudaSuccess) {
        std::cerr << "Failed to synchronize CUDA stream: " << cudaGetErrorString(result) << std::endl;
    }
}
