C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_malloc.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_math.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_memcpy_s.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_memory.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_search.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_share.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_stdio_config.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_terminate.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wconio.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wctype.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wdirect.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wio.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wprocess.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wstdio.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wstdlib.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wstring.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wtime.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\crtdbg.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\ctype.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\errno.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\float.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\malloc.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\math.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\stddef.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\stdio.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\stdlib.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\string.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\sys\stat.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\sys\types.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\wchar.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\ammintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cfloat
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\climits
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cmath
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\concurrencysal.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\crtdefs.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cstddef
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cstdint
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cstdio
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cstdlib
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cstring
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\cwchar
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\eh.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\emmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\exception
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\immintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\initializer_list
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\intrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\intrin0.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\intrin0.inl.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\limits
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\limits.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\mmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\new
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\nmmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\pmmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\sal.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\setjmp.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\smmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\stdint.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tmmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\use_ansi.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vadefs.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new_debug.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_string.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\wmmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xatomic.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xkeycheck.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtr1common
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\yvals.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\yvals_core.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\zmmintrin.h
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_iter_core.hpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_minmax.hpp
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\builtin_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\channel_descriptor.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\common_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\cudacc_ext.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\device_double_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\device_double_functions.hpp
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\device_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\device_functions.hpp
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\host_config.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\host_defines.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\math_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\math_functions.hpp
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\sm_100_rt.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\sm_70_rt.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\sm_80_rt.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\crt\sm_90_rt.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\cuda_device_runtime_api.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\cuda_runtime.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\cuda_runtime_api.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\device_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\device_launch_parameters.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\device_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\driver_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\driver_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\library_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_20_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_20_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_30_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_32_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_32_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_35_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_35_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_60_atomic_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\sm_61_intrinsics.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\surface_indirect_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\surface_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\texture_indirect_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\texture_types.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\vector_functions.h
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\vector_functions.hpp
C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include\vector_types.h
F:\Catechese\EditeurAudioVideo\ImageMatter\include\alpha_codec.cuh
F:\Catechese\EditeurAudioVideo\ImageMatter\include\alpha_codec.h
