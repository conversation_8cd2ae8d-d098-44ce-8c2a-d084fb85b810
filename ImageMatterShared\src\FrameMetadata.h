#pragma once

#include <cstdint>
#include <cstring>
#include <vector>
#include "HeadDetector.h" // For Box struct

/**
 * Maximum number of head detections per frame
 * This allows for fixed-size memory mapping
 */
constexpr int MAX_HEAD_DETECTIONS = 16;

/**
 * Maximum size for encoded alpha data per frame (in bytes)
 * This should be sufficient for compressed alpha data
 */
constexpr size_t MAX_ENCODED_ALPHA_SIZE = 1024 * 1024; // 1MB per frame

/**
 * Frame metadata structure for memory-mapped persistence between processing passes
 * This structure contains all data that needs to persist between video processing steps
 * Designed for fixed-size memory mapping to avoid pointer issues
 */
struct FrameMetadata {
    // Frame identification
    int frameIndex;
    double timestamp;

    // Processing status flags
    bool hasInitialAlpha;
    bool hasHeadDetection;
    bool hasBodyProcessing;
    bool hasHeadProcessing;

    // Encoded alpha matte data (from initial alpha step)
    size_t encodedAlphaSize;
    unsigned char encodedAlpha[MAX_ENCODED_ALPHA_SIZE];

    // Head detection results
    int numHeadDetections;
    Box headDetections[MAX_HEAD_DETECTIONS];

    // Additional processing metadata (can be extended as needed)
    struct {
        int numBodyRegionsProcessed;
        int numHeadRegionsProcessed;
        float averageConfidence;
        // Reserved space for future extensions
        float reserved[16];
    } processingStats;

    // Constructor
    FrameMetadata() {
        Reset();
    }

    // Reset all data to initial state
    void Reset() {
        frameIndex = -1;
        timestamp = -1.0;
        hasInitialAlpha = false;
        hasHeadDetection = false;
        hasBodyProcessing = false;
        hasHeadProcessing = false;
        encodedAlphaSize = 0;
        numHeadDetections = 0;
        processingStats = {};

        // Clear arrays
        memset(encodedAlpha, 0, sizeof(encodedAlpha));
        memset(headDetections, 0, sizeof(headDetections));
    }

    // Validate the metadata structure
    bool IsValid() const {
        return frameIndex >= 0 &&
               timestamp >= 0.0 &&
               encodedAlphaSize <= MAX_ENCODED_ALPHA_SIZE &&
               numHeadDetections <= MAX_HEAD_DETECTIONS;
    }

    // Set encoded alpha data
    bool SetEncodedAlpha(const unsigned char* data, size_t size) {
        if (size > MAX_ENCODED_ALPHA_SIZE) {
            return false;
        }
        encodedAlphaSize = size;
        memcpy(encodedAlpha, data, size);
        hasInitialAlpha = true;
        return true;
    }

    // Get encoded alpha data
    const unsigned char* GetEncodedAlpha() const {
        return hasInitialAlpha ? encodedAlpha : nullptr;
    }

    // Set head detection results
    bool SetHeadDetections(const std::vector<Box>& detections) {
        if (detections.size() > MAX_HEAD_DETECTIONS) {
            return false;
        }
        numHeadDetections = static_cast<int>(detections.size());
        for (size_t i = 0; i < detections.size(); ++i) {
            headDetections[i] = detections[i];
        }
        hasHeadDetection = true;
        return true;
    }

    // Get head detection results as vector
    std::vector<Box> GetHeadDetections() const {
        std::vector<Box> result;
        if (hasHeadDetection) {
            result.reserve(numHeadDetections);
            for (int i = 0; i < numHeadDetections; ++i) {
                result.push_back(headDetections[i]);
            }
        }
        return result;
    }
};

// Ensure the structure is properly aligned for memory mapping
static_assert(sizeof(FrameMetadata) % 8 == 0, "FrameMetadata must be 8-byte aligned for memory mapping");
