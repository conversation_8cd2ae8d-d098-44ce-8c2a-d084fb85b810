#pragma once

#include "ProcessingStep.h"
#include "FrameMetadata.h"
#include "ImageMatting.h"
#include <memory>

/**
 * Processing step for generating initial alpha matte
 * Uses InsPyReNet or similar model to generate initial alpha matte and encode it
 */
class FinalInitialAlphaStep : public ProcessingStep {
public:
    FinalInitialAlphaStep();
    virtual ~FinalInitialAlphaStep();

    // ProcessingStep interface implementation
    bool Initialize(int width, int height, CUcontext cudaContext, cudaStream_t stream) override;
    bool Process(void* inputNv12Data, size_t inputPitch, FrameMetadata& frameMetadata) override;
    const char* GetStepName() const override { return "FinalInitialAlpha"; }
    bool IsInitialized() const override { return m_initialized; }

private:
    // AI model for initial alpha matting
    std::unique_ptr<ImageMatting> m_initialImageMatter;

    // CUDA buffers for processing
    float* m_rgbBuffer;
    size_t m_rgbBufferSize;

    // Processing parameters
    int m_width;
    int m_height;
    CUcontext m_cudaContext;
    cudaStream_t m_stream;
    bool m_initialized;

    // Compression buffer for alpha encoding
    unsigned char* m_compressionBuffer;
    size_t m_compressionBufferSize;

    // Helper methods
    bool AllocateBuffers();
    void DeallocateBuffers();
    bool EncodeAlpha(float* alphaBuffer, unsigned char** encodedData, size_t* encodedSize);
};
