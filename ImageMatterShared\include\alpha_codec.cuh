#ifndef ALPHA_CODEC_CUH
#define ALPHA_CODEC_CUH

#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <cstdint>

// Kernel launch function declarations (these will be called from C++ code)
extern "C" {
void launch_compress_columns(const uint8_t* d_input,
	int width,
	int height,
	int* d_column_headers,
	int* d_output,
	cudaStream_t stream);

void launch_add_column_headers(int* d_column_headers,
	int width,
	cudaStream_t stream);

void launch_compress_lines(const int* d_column_headers,
	const int* d_input,
	int* d_output,
	int width,
	cudaStream_t stream);

void launch_decompress(const int* d_column_headers,
	const int* d_input,
	uint8_t* d_output,
	int width,
	int height,
	cudaStream_t stream);
}

#endif // ALPHA_CODEC_CUH