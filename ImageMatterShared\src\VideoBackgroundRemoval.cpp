#include "VideoBackgroundRemoval.h"
#include "FrameProcessor.h"
#include "DirectVideoReader.h"
#include "DirectVideoWriterAlphaCuda.h"
#include "ImageMattingFactory.h"
#include "Helpers.h"
#include "AsyncDirectVideoReader.h"
#include "FrameMetadataStorage.h"
#include "FrameMetadata.h"
#include "ProcessingStep.h"
#include "ProcessBodyRegionsStep.h"
#include <cuda.h>
#include <cuda_runtime.h>
#include <string>
#include <memory>
#include <atomic>
#include <functional>
#include <chrono>
#include <iostream>
#include <iomanip>
#include <vector>
#include <set>
#include <algorithm>
#include <cmath>

extern "C" {
#include <libavutil/log.h>
}

// Structure to hold compressed alpha data for each frame (for two-phase processing)
struct FrameAlphaData {
    int frameIndex;
    unsigned char* encodedAlpha;
    size_t encodedSize;
    double timestamp;
};

// Forward declarations
int ProcessMultiPass(
    const std::wstring& inputPath,
    const std::wstring& outputPath,
    EngineType engine,
    CUcontext context,
    cudaStream_t stream,
    ProgressCallback progressCb,
    void* userData
);

// Helper function to convert wstring to string
std::string WStringToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

// One-phase processing implementation
int ProcessOnePhase(
    const std::string& inputPath,
    const std::string& outputPath,
    CUcontext context,
    ProgressCallback progressCb,
    void* userData
) {
    try {
        // Create regular video reader (hardware accelerated for speed)
        auto reader = DirectVideoReader::Create(inputPath, context, true);
        if (!reader) {
            std::cerr << "Failed to create video reader for: " << inputPath << std::endl;
            return 1;
        }

        // Create CUDA stream for processing
        cudaStream_t processingStream;
        CUDA_CHECK(cudaStreamCreate(&processingStream));

        // Initialize frame processor
        auto processor = std::make_unique<FrameProcessor>();
        if (!processor->Initialize(reader->GetWidth(), reader->GetHeight(), processingStream)) {
            std::cerr << "Failed to initialize FrameProcessor" << std::endl;
            cudaStreamDestroy(processingStream);
            return 1;
        }

        // Configure output for CUDA ProRes 4444 with alpha
        DirectVideoWriterAlphaCuda::OutputConfig outputConfig;
        outputConfig.width = reader->GetWidth();
        outputConfig.height = reader->GetHeight();
        outputConfig.frameRate = reader->GetFrameRate();
        outputConfig.outputPath = outputPath;
        outputConfig.UseProRes4444();

        // Create CUDA alpha video writer
        auto writer = DirectVideoWriterAlphaCuda::Create(outputConfig, context);
        if (!writer) {
            std::cerr << "Failed to create CUDA ProRes video writer" << std::endl;
            processor->Cleanup();
            cudaStreamDestroy(processingStream);
            return 1;
        }

        // Allocate buffers for hardware-accelerated video reading (NV12 format)
        int nv12Height = reader->GetHeight() + reader->GetHeight() / 2;
        void* d_inputNv12Array = nullptr;
        size_t inputPitch = 0;
        CUDA_CHECK(cudaMallocPitch(&d_inputNv12Array, &inputPitch, reader->GetWidth(), nv12Height));

        // Allocate buffer for RGBA output
        size_t rgbaBufferSize = reader->GetWidth() * reader->GetHeight() * 4 * sizeof(float);
        void* d_rgbaBuffer = nullptr;
        CUDA_CHECK(cudaMalloc(&d_rgbaBuffer, rgbaBufferSize));

        int frameCount = 0;
        double timestamp = 0.0;
        auto startTime = std::chrono::high_resolution_clock::now();

        // Process all frames
        while (true) {
            // Read frame
            timestamp = reader->ReadFrame(d_inputNv12Array, inputPitch * nv12Height, inputPitch);
            if (timestamp < 0.0) break;

            // Process frame with FrameProcessor (AI Alpha Matting + Background Estimation)
            if (!processor->ProcessFrame(d_inputNv12Array, inputPitch, static_cast<float*>(d_rgbaBuffer))) {
                std::cerr << "Failed to process frame " << frameCount << " with FrameProcessor" << std::endl;
                break;
            }

            // Write frame to ProRes
            if (!writer->WriteFrame(d_rgbaBuffer, rgbaBufferSize)) {
                std::cerr << "Failed to write frame " << frameCount << std::endl;
                break;
            }

            frameCount++;

            // Call progress callback if provided
            if (progressCb) {
                if (!progressCb(frameCount, 0, userData)) {
                    // User requested cancellation
                    break;
                }
            }
        }

        // Finalize video
        writer->Finalize();

        // Clean up
        processor->Cleanup();
        if (d_inputNv12Array) cudaFree(d_inputNv12Array);
        if (d_rgbaBuffer) cudaFree(d_rgbaBuffer);
        if (processingStream) cudaStreamDestroy(processingStream);

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count() / 1000.0;

        std::cout << "One-phase processing completed: " << frameCount << " frames processed in "
                 << std::fixed << std::setprecision(2) << duration << " seconds" << std::endl;

        return 0; // Success
    }
    catch (const std::exception& e) {
        std::cerr << "Error in one-phase processing: " << e.what() << std::endl;
        return 1;
    }
}

// Two-phase processing implementation
int ProcessTwoPhase(
    const std::string& inputPath,
    const std::string& outputPath,
    CUcontext context,
    ProgressCallback progressCb,
    void* userData
) {
    try {
        // Create regular video reader (hardware accelerated for speed)
        auto reader = DirectVideoReader::Create(inputPath, context, true);
        if (!reader) {
            std::cerr << "Failed to create video reader for: " << inputPath << std::endl;
            return 1;
        }

        // Create CUDA stream for processing
        cudaStream_t processingStream;
        CUDA_CHECK(cudaStreamCreate(&processingStream));

        // Allocate buffers for hardware-accelerated video reading (NV12 format)
        int nv12Height = reader->GetHeight() + reader->GetHeight() / 2;
        void* d_inputNv12Array = nullptr;
        size_t inputPitch = 0;
        CUDA_CHECK(cudaMallocPitch(&d_inputNv12Array, &inputPitch, reader->GetWidth(), nv12Height));

        // Vector to store compressed alpha data for all frames
        std::vector<FrameAlphaData> frameAlphaDataList;

        // ===== PHASE 1: Initial Alpha Generation =====
        std::cout << "Phase 1: Initial Alpha Generation" << std::endl;

        // Initialize frame processor for initial matting only
        auto processor = std::make_unique<FrameProcessor>();
        if (!processor->InitializeInitialMatte(reader->GetWidth(), reader->GetHeight(), processingStream)) {
            std::cerr << "Failed to initialize FrameProcessor for initial matting" << std::endl;
            cudaStreamDestroy(processingStream);
            if (d_inputNv12Array) cudaFree(d_inputNv12Array);
            return 1;
        }

        // Timing for phase 1
        int frameCount = 0;
        double timestamp = 0.0;
        auto phase1StartTime = std::chrono::high_resolution_clock::now();

        // Process all frames for initial alpha generation
        while (true) {
            // Read frame
            timestamp = reader->ReadFrame(d_inputNv12Array, inputPitch * nv12Height, inputPitch);
            if (timestamp < 0.0) break;

            // Generate initial alpha and compress it
            unsigned char* encodedAlpha = nullptr;
            size_t encodedSize = 0;
            if (!processor->FindInitialAlpha(d_inputNv12Array, inputPitch, &encodedAlpha, &encodedSize)) {
                std::cerr << "Failed to generate initial alpha for frame " << frameCount << std::endl;
                break;
            }

            // Store compressed alpha data
            FrameAlphaData frameData;
            frameData.frameIndex = frameCount;
            frameData.encodedAlpha = encodedAlpha;
            frameData.encodedSize = encodedSize;
            frameData.timestamp = timestamp;
            frameAlphaDataList.push_back(frameData);

            frameCount++;

            // Call progress callback if provided (phase 1 is first half of progress)
            if (progressCb) {
                if (!progressCb(frameCount, frameCount * 2, userData)) {
                    // User requested cancellation
                    break;
                }
            }
        }

        auto phase1EndTime = std::chrono::high_resolution_clock::now();
        double phase1Duration = std::chrono::duration<double>(phase1EndTime - phase1StartTime).count();

        std::cout << "Phase 1 completed: " << frameCount << " frames processed in "
                 << std::fixed << std::setprecision(2) << phase1Duration << " seconds" << std::endl;

        // Clean up initial matting processor to free GPU memory
        processor->Cleanup();
        processor.reset();

        // ===== PHASE 2: Alpha Refinement and Final Processing =====
        std::cout << "Phase 2: Alpha Refinement and Final Processing" << std::endl;

        // Initialize frame processor for refinement
        processor = std::make_unique<FrameProcessor>();
        if (!processor->InitializeRefinement(reader->GetWidth(), reader->GetHeight(), processingStream)) {
            std::cerr << "Failed to initialize FrameProcessor for refinement" << std::endl;
            // Clean up alpha data
            for (auto& frameData : frameAlphaDataList) {
                if (frameData.encodedAlpha) free(frameData.encodedAlpha);
            }
            cudaStreamDestroy(processingStream);
            if (d_inputNv12Array) cudaFree(d_inputNv12Array);
            return 1;
        }

        // Configure output for CUDA ProRes 4444 with alpha
        DirectVideoWriterAlphaCuda::OutputConfig outputConfig;
        outputConfig.width = reader->GetWidth();
        outputConfig.height = reader->GetHeight();
        outputConfig.frameRate = reader->GetFrameRate();
        outputConfig.outputPath = outputPath;
        outputConfig.UseProRes4444();

        // Create CUDA alpha video writer
        auto writer = DirectVideoWriterAlphaCuda::Create(outputConfig, context);
        if (!writer) {
            std::cerr << "Failed to create CUDA ProRes video writer" << std::endl;
            processor->Cleanup();
            // Clean up alpha data
            for (auto& frameData : frameAlphaDataList) {
                if (frameData.encodedAlpha) free(frameData.encodedAlpha);
            }
            cudaStreamDestroy(processingStream);
            if (d_inputNv12Array) cudaFree(d_inputNv12Array);
            return 1;
        }

        // Allocate buffer for RGBA output
        size_t rgbaBufferSize = reader->GetWidth() * reader->GetHeight() * 4 * sizeof(float);
        void* d_rgbaBuffer = nullptr;
        CUDA_CHECK(cudaMalloc(&d_rgbaBuffer, rgbaBufferSize));

        // Reset reader for second pass
        reader.reset();
        reader = DirectVideoReader::Create(inputPath, context, true);

        // Timing for phase 2
        auto phase2StartTime = std::chrono::high_resolution_clock::now();

        // Process all frames for refinement and encoding
        int currentFrameIndex = 0;
        while (true) {
            // Read frame
            timestamp = reader->ReadFrame(d_inputNv12Array, inputPitch * nv12Height, inputPitch);
            if (timestamp < 0.0) break;

            // Get corresponding alpha data
            if (currentFrameIndex >= frameAlphaDataList.size()) {
                std::cerr << "Frame index mismatch in phase 2" << std::endl;
                break;
            }

            FrameAlphaData& frameData = frameAlphaDataList[currentFrameIndex];

            // Refine alpha and generate final RGBA
            if (!processor->RefineInitialAlpha(d_inputNv12Array, inputPitch,
                                             frameData.encodedAlpha, frameData.encodedSize,
                                             static_cast<float*>(d_rgbaBuffer))) {
                std::cerr << "Failed to refine alpha for frame " << currentFrameIndex << std::endl;
                break;
            }

            // Encode frame
            if (!writer->WriteFrame(d_rgbaBuffer, rgbaBufferSize)) {
                std::cerr << "Failed to write frame " << currentFrameIndex << std::endl;
                break;
            }

            currentFrameIndex++;

            // Call progress callback if provided (phase 2 is second half of progress)
            if (progressCb) {
                if (!progressCb(frameCount + currentFrameIndex, frameCount * 2, userData)) {
                    // User requested cancellation
                    break;
                }
            }
        }

        auto phase2EndTime = std::chrono::high_resolution_clock::now();
        double phase2Duration = std::chrono::duration<double>(phase2EndTime - phase2StartTime).count();

        std::cout << "Phase 2 completed: " << currentFrameIndex << " frames processed in "
                 << std::fixed << std::setprecision(2) << phase2Duration << " seconds" << std::endl;

        // Finalize video
        writer->Finalize();

        // Clean up frame processor
        processor->Cleanup();

        // Clean up compressed alpha data
        for (auto& frameData : frameAlphaDataList) {
            if (frameData.encodedAlpha) {
                free(frameData.encodedAlpha);
            }
        }

        // Cleanup buffers
        if (d_inputNv12Array) cudaFree(d_inputNv12Array);
        if (d_rgbaBuffer) cudaFree(d_rgbaBuffer);
        if (processingStream) cudaStreamDestroy(processingStream);

        // Final timing summary
        double totalDuration = phase1Duration + phase2Duration;
        std::cout << "Two-phase processing completed: " << frameCount << " frames processed in "
                 << std::fixed << std::setprecision(2) << totalDuration << " seconds" << std::endl;

        return 0; // Success
    }
    catch (const std::exception& e) {
        std::cerr << "Error in two-phase processing: " << e.what() << std::endl;
        return 1;
    }
}

// Multi-pass processing implementation
int ProcessMultiPass(
    const std::wstring& inputPath,
    const std::wstring& outputPath,
    EngineType engine,
    CUcontext context,
    cudaStream_t stream,
    ProgressCallback progressCb,
    void* userData
) {
    try {
        auto startTime = std::chrono::high_resolution_clock::now();

        // Create asynchronous video reader
        std::string inputPathStr(inputPath.begin(), inputPath.end());
        auto reader = AsyncDirectVideoReader::Create(inputPathStr, context, true, 3);
        if (!reader) {
            std::wcerr << L"Failed to create async video reader for: " << inputPath << std::endl;
            return 1;
        }

        int videoWidth = reader->GetWidth();
        int videoHeight = reader->GetHeight();
        int totalFrames = reader->GetFrameCount();

        std::cout << "Video dimensions: " << videoWidth << "x" << videoHeight << std::endl;
        std::cout << "Total frames: " << totalFrames << std::endl;

        // Create frame metadata storage
        std::wstring tempPath = L"frame_metadata_temp.dat";
        auto metadataStorage = FrameMetadataStorage::Create(tempPath, totalFrames, true);
        if (!metadataStorage) {
            std::cerr << "Failed to create frame metadata storage" << std::endl;
            return 1;
        }

        // Create CUDA stream for processing
        cudaStream_t processingStream;
        CUDA_CHECK(cudaStreamCreate(&processingStream));

        // ===== PASS 1: Initial Alpha Generation =====
        std::cout << "\n=== Pass 1: Initial Alpha Generation ===" << std::endl;

        auto initialAlphaStep = ProcessingStepFactory::CreateStep(ProcessingStepFactory::StepType::FINAL_INITIAL_ALPHA);
        if (!initialAlphaStep || !initialAlphaStep->Initialize(videoWidth, videoHeight, context, processingStream)) {
            std::cerr << "Failed to initialize FinalInitialAlphaStep" << std::endl;
            return 1;
        }

        // Process all frames for initial alpha
        int frameIndex = 0;
        while (true) {
            void* frameData = nullptr;
            size_t framePitch = 0;
            double timestamp = reader->ReadFrame(&frameData, framePitch);

            if (timestamp < 0.0) break; // End of video

            FrameMetadata frameMetadata;
            frameMetadata.frameIndex = frameIndex;
            frameMetadata.timestamp = timestamp;

            if (!initialAlphaStep->Process(frameData, framePitch, frameMetadata)) {
                std::cerr << "Failed to process frame " << frameIndex << " in initial alpha step" << std::endl;
                break;
            }

            if (!metadataStorage->SetFrameMetadata(frameIndex, frameMetadata)) {
                std::cerr << "Failed to store frame metadata for frame " << frameIndex << std::endl;
                break;
            }

            frameIndex++;

            // Progress callback for pass 1
            if (progressCb && !progressCb(frameIndex, totalFrames * 4, userData)) {
                std::cout << "Processing cancelled by user" << std::endl;
                return 1;
            }
        }

        // Clean up initial alpha step to free memory
        initialAlphaStep.reset();
        reader->Reset(); // Reset reader for next pass

        std::cout << "Pass 1 completed: " << frameIndex << " frames processed" << std::endl;

        // ===== PASS 2: Head Detection =====
        std::cout << "\n=== Pass 2: Head Detection ===" << std::endl;

        auto detectHeadsStep = ProcessingStepFactory::CreateStep(ProcessingStepFactory::StepType::DETECT_HEADS);
        if (!detectHeadsStep || !detectHeadsStep->Initialize(videoWidth, videoHeight, context, processingStream)) {
            std::cerr << "Failed to initialize DetectHeadsStep" << std::endl;
            return 1;
        }

        // Process all frames for head detection
        frameIndex = 0;
        reader->Reset();
        while (true) {
            void* frameData = nullptr;
            size_t framePitch = 0;
            double timestamp = reader->ReadFrame(&frameData, framePitch);

            if (timestamp < 0.0) break; // End of video

            FrameMetadata* frameMetadata = metadataStorage->GetFrameMetadata(frameIndex);
            if (!frameMetadata) {
                std::cerr << "Failed to get frame metadata for frame " << frameIndex << std::endl;
                break;
            }

            if (!detectHeadsStep->Process(frameData, framePitch, *frameMetadata)) {
                std::cerr << "Failed to process frame " << frameIndex << " in head detection step" << std::endl;
                break;
            }

            if (!metadataStorage->SetFrameMetadata(frameIndex, *frameMetadata)) {
                std::cerr << "Failed to update frame metadata for frame " << frameIndex << std::endl;
                break;
            }

            frameIndex++;

            // Progress callback for pass 2
            if (progressCb && !progressCb(totalFrames + frameIndex, totalFrames * 4, userData)) {
                std::cout << "Processing cancelled by user" << std::endl;
                return 1;
            }
        }

        // Clean up head detection step
        detectHeadsStep.reset();
        reader->Reset();

        std::cout << "Pass 2 completed: " << frameIndex << " frames processed" << std::endl;

        // ===== HEAD SIZE ANALYSIS =====
        std::cout << "\n=== Analyzing Head Sizes ===" << std::endl;

        std::set<int> requiredHeadSizes;
        for (int i = 0; i < totalFrames; ++i) {
            FrameMetadata* frameMetadata = metadataStorage->GetFrameMetadata(i);
            if (frameMetadata && frameMetadata->hasHeadDetection) {
                for (int j = 0; j < frameMetadata->numHeadDetections; ++j) {
                    const auto& head = frameMetadata->headDetections[j];
                    int headSize = std::max(head.width, head.height);

                    // Map head size to InsPyReNet model sizes
                    int requiredModelSize = (headSize <= 320) ? 320 : (headSize <= 640) ? 640 : 1024;
                    requiredHeadSizes.insert(requiredModelSize);
                }
            }
        }

        std::cout << "Required head model sizes: ";
        for (int size : requiredHeadSizes) {
            std::cout << size << " ";
        }
        std::cout << std::endl;

        // ===== PASS 3+: Head Region Processing (one pass per head size) =====
        int passNumber = 3;
        for (int headModelSize : requiredHeadSizes) {
            std::cout << "\n=== Pass " << passNumber << ": Head Region Processing (size " << headModelSize << ") ===" << std::endl;

            auto headRegionsStep = ProcessingStepFactory::CreateHeadRegionsStep(headModelSize);
            if (!headRegionsStep || !headRegionsStep->Initialize(videoWidth, videoHeight, context, processingStream)) {
                std::cerr << "Failed to initialize ProcessHeadRegionsStep for size " << headModelSize << std::endl;
                return 1;
            }

            // Process all frames for this head size
            frameIndex = 0;
            reader->Reset();
            while (true) {
                void* frameData = nullptr;
                size_t framePitch = 0;
                double timestamp = reader->ReadFrame(&frameData, framePitch);

                if (timestamp < 0.0) break; // End of video

                FrameMetadata* frameMetadata = metadataStorage->GetFrameMetadata(frameIndex);
                if (!frameMetadata) {
                    std::cerr << "Failed to get frame metadata for frame " << frameIndex << std::endl;
                    break;
                }

                if (!headRegionsStep->Process(frameData, framePitch, *frameMetadata)) {
                    std::cerr << "Failed to process frame " << frameIndex << " in head regions step" << std::endl;
                    break;
                }

                if (!metadataStorage->SetFrameMetadata(frameIndex, *frameMetadata)) {
                    std::cerr << "Failed to update frame metadata for frame " << frameIndex << std::endl;
                    break;
                }

                frameIndex++;

                // Progress callback for head processing passes
                if (progressCb && !progressCb((totalFrames * 2) + frameIndex, totalFrames * 4, userData)) {
                    std::cout << "Processing cancelled by user" << std::endl;
                    return 1;
                }
            }

            // Clean up head regions step
            headRegionsStep.reset();
            reader->Reset();

            std::cout << "Pass " << passNumber << " completed: " << frameIndex << " frames processed" << std::endl;
            passNumber++;
        }

        // ===== FINAL PASS: Body Region Processing with Video Output =====
        std::cout << "\n=== Final Pass: Body Region Processing with Video Output ===" << std::endl;

        auto bodyRegionsStep = ProcessingStepFactory::CreateStep(ProcessingStepFactory::StepType::PROCESS_BODY_REGIONS);
        if (!bodyRegionsStep || !bodyRegionsStep->Initialize(videoWidth, videoHeight, context, processingStream)) {
            std::cerr << "Failed to initialize ProcessBodyRegionsStep" << std::endl;
            return 1;
        }

        // Create video writer for final output
        DirectVideoWriterAlphaCuda::OutputConfig outputConfig;
        outputConfig.width = videoWidth;
        outputConfig.height = videoHeight;
        outputConfig.frameRate = reader->GetFrameRate();
        std::string outputPathStr(outputPath.begin(), outputPath.end());
        outputConfig.outputPath = outputPathStr;

        auto writer = DirectVideoWriterAlphaCuda::Create(outputConfig, context);
        if (!writer) {
            std::wcerr << L"Failed to create video writer for: " << outputPath << std::endl;
            return 1;
        }

        // Allocate RGBA output buffer
        void* d_rgbaBuffer = nullptr;
        size_t rgbaBufferSize = videoWidth * videoHeight * 4 * sizeof(float);
        CUDA_CHECK(cudaMalloc(&d_rgbaBuffer, rgbaBufferSize));

        // Process all frames for body regions and write output
        frameIndex = 0;
        reader->Reset();
        while (true) {
            void* frameData = nullptr;
            size_t framePitch = 0;
            double timestamp = reader->ReadFrame(&frameData, framePitch);

            if (timestamp < 0.0) break; // End of video

            FrameMetadata* frameMetadata = metadataStorage->GetFrameMetadata(frameIndex);
            if (!frameMetadata) {
                std::cerr << "Failed to get frame metadata for frame " << frameIndex << std::endl;
                break;
            }

            if (!bodyRegionsStep->Process(frameData, framePitch, *frameMetadata)) {
                std::cerr << "Failed to process frame " << frameIndex << " in body regions step" << std::endl;
                break;
            }

            // Generate final RGBA output from processed alpha and original frame
            // Extract foreground using the formula: foreground = (original - (1-alpha) * background) / alpha
            // This combines the refined alpha with the original RGB data to create the final output
            ProcessBodyRegionsStep* bodyStep = dynamic_cast<ProcessBodyRegionsStep*>(bodyRegionsStep.get());
            if (!bodyStep || !bodyStep->GenerateFinalOutput(frameData, framePitch, *frameMetadata, static_cast<float*>(d_rgbaBuffer))) {
                std::cerr << "Failed to generate final RGBA output for frame " << frameIndex << std::endl;
                break;
            }

            if (!writer->WriteFrame(d_rgbaBuffer, rgbaBufferSize)) {
                std::cerr << "Failed to write frame " << frameIndex << std::endl;
                break;
            }

            frameIndex++;

            // Progress callback for final pass
            if (progressCb && !progressCb((totalFrames * 3) + frameIndex, totalFrames * 4, userData)) {
                std::cout << "Processing cancelled by user" << std::endl;
                break;
            }
        }

        // Finalize video output
        writer->Finalize();

        // Clean up
        bodyRegionsStep.reset();
        if (d_rgbaBuffer) cudaFree(d_rgbaBuffer);
        if (processingStream) cudaStreamDestroy(processingStream);

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count() / 1000.0;

        std::cout << "Final pass completed: " << frameIndex << " frames processed" << std::endl;
        std::cout << "Multi-pass processing completed: " << frameIndex << " frames processed in "
                 << std::fixed << std::setprecision(2) << duration << " seconds" << std::endl;

        return 0; // Success
    }
    catch (const std::exception& e) {
        std::cerr << "Error in ProcessMultiPass: " << e.what() << std::endl;
        return 1;
    }
}


// Main function: Returns 0 on success, nonzero on error/cancel
int VideoBackgroundRemoval(
    const std::wstring& inputVideo,
    const std::wstring& outputVideo,
    EngineType engine,
    ProgressCallback progressCb,
    void* userData
) {
    try {
        // Initialize CUDA context
        CUcontext cudaContext;
        CUresult result = cuCtxCreate(&cudaContext, 0, 0);
        if (result != CUDA_SUCCESS) {
            std::cerr << "Failed to create CUDA context" << std::endl;
            return 1;
        }

        // Set the context for this thread
        cuCtxSetCurrent(cudaContext);

        // Create CUDA stream for processing
        cudaStream_t stream;
        CUDA_CHECK(cudaStreamCreate(&stream));

        // Process the video using multi-pass architecture
        int processResult = ProcessMultiPass(inputVideo, outputVideo, engine, cudaContext, stream, progressCb, userData);

        // Cleanup
        CUDA_CHECK(cudaStreamDestroy(stream));
        cuCtxDestroy(cudaContext);

        return processResult;
    }
    catch (const std::exception& e) {
        std::cerr << "Error in VideoBackgroundRemoval: " << e.what() << std::endl;
        return 1;
    }
}

// Overload with twoPhase parameter for backward compatibility
int VideoBackgroundRemoval(
    const std::wstring& inputVideo,
    const std::wstring& outputVideo,
    EngineType engine,
    bool twoPhase,
    ProgressCallback progressCb,
    void* userData
) {
    // Call the main function (twoPhase parameter is ignored as we always use multi-pass now)
    return VideoBackgroundRemoval(inputVideo, outputVideo, engine, progressCb, userData);
}
