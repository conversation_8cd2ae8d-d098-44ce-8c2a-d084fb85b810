// CudaProResKernels.cu
#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <cstdint>
#include <math_constants.h>

#ifdef __CUDACC__


// ProRes 4444 quantization tables (10-bit)
__constant__ uint16_t d_quantTables[4][64] = {
    // Y component quantization table
    {
        4, 7, 9, 11, 13, 14, 15, 63,
        7, 7, 11, 12, 14, 15, 63, 63,
        9, 11, 13, 14, 15, 63, 63, 63,
        11, 12, 14, 15, 63, 63, 63, 63,
        13, 14, 15, 63, 63, 63, 63, 63,
        14, 15, 63, 63, 63, 63, 63, 63,
        15, 63, 63, 63, 63, 63, 63, 63,
        63, 63, 63, 63, 63, 63, 63, 63
    },
    // U component quantization table
    {
        4, 7, 9, 11, 13, 14, 15, 63,
        7, 7, 11, 12, 14, 15, 63, 63,
        9, 11, 13, 14, 15, 63, 63, 63,
        11, 12, 14, 15, 63, 63, 63, 63,
        13, 14, 15, 63, 63, 63, 63, 63,
        14, 15, 63, 63, 63, 63, 63, 63,
        15, 63, 63, 63, 63, 63, 63, 63,
        63, 63, 63, 63, 63, 63, 63, 63
    },
    // V component quantization table
    {
        4, 7, 9, 11, 13, 14, 15, 63,
        7, 7, 11, 12, 14, 15, 63, 63,
        9, 11, 13, 14, 15, 63, 63, 63,
        11, 12, 14, 15, 63, 63, 63, 63,
        13, 14, 15, 63, 63, 63, 63, 63,
        14, 15, 63, 63, 63, 63, 63, 63,
        15, 63, 63, 63, 63, 63, 63, 63,
        63, 63, 63, 63, 63, 63, 63, 63
    },
    // Alpha component quantization table (lossless)
    {
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1,
        1, 1, 1, 1, 1, 1, 1, 1
    }
};

// DCT basis functions (precomputed for 8x8 blocks)
__constant__ float d_dctBasis[64][64];

// CUDA kernel to convert RGBA to YUVA 4:4:4:4 10-bit
__global__ void rgbaToYuva444_10bitKernel(
    float* rgbaData, 
    uint16_t* yuvaData, 
    int width, 
    int height
) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x >= width || y >= height) return;

    int rgbaIndex = (y * width + x) * 4;
    float R = rgbaData[rgbaIndex + 0];
    float G = rgbaData[rgbaIndex + 1];
    float B = rgbaData[rgbaIndex + 2];
    float A = rgbaData[rgbaIndex + 3];

    // Convert RGB to YUV using BT.709 coefficients (HD standard)
    float Y = 0.2126f * R + 0.7152f * G + 0.0722f * B;
    float U = -0.1146f * R - 0.3854f * G + 0.5000f * B + 0.5f;
    float V = 0.5000f * R - 0.4542f * G - 0.0458f * B + 0.5f;

    // Scale to 10-bit range (0-1023)
    // Y: 64-940 for 10-bit (limited range)
    // UV: 64-960 for 10-bit (limited range)
    // A: 0-1023 for 10-bit alpha (full range)
    Y = Y * 876.0f + 64.0f;   // Scale to 10-bit Y range
    U = U * 896.0f + 64.0f;   // Scale to 10-bit U range
    V = V * 896.0f + 64.0f;   // Scale to 10-bit V range
    A = A * 1023.0f;          // Scale alpha to 10-bit range

    // Clamp values to valid ranges
    Y = fmaxf(64.0f, fminf(940.0f, Y));
    U = fmaxf(64.0f, fminf(960.0f, U));
    V = fmaxf(64.0f, fminf(960.0f, V));
    A = fmaxf(0.0f, fminf(1023.0f, A));

    // Calculate planar indices for YUVA444 format
    int pixelIndex = y * width + x;
    int planeSize = width * height;

    // Write to separate planes (Y, U, V, A) as 10-bit values
    yuvaData[pixelIndex] = (uint16_t)Y;                     // Y plane
    yuvaData[pixelIndex + planeSize] = (uint16_t)U;         // U plane
    yuvaData[pixelIndex + 2 * planeSize] = (uint16_t)V;     // V plane
    yuvaData[pixelIndex + 3 * planeSize] = (uint16_t)A;     // A plane
}

// CUDA kernel for forward DCT transform (8x8 blocks)
__global__ void forwardDCTKernel(
    uint16_t* yuvaData,
    int16_t* dctCoeffs,
    int width,
    int height,
    int componentStride,
    int component
) {
    int blockX = blockIdx.x;
    int blockY = blockIdx.y;
    int threadX = threadIdx.x;
    int threadY = threadIdx.y;

    // Each block processes an 8x8 DCT block
    if (blockX * 8 >= width || blockY * 8 >= height) return;

    __shared__ float blockData[8][8];
    __shared__ float dctData[8][8];

    // Load 8x8 block into shared memory
    int srcX = blockX * 8 + threadX;
    int srcY = blockY * 8 + threadY;
    
    if (srcX < width && srcY < height) {
        int srcIndex = component * componentStride + srcY * width + srcX;
        blockData[threadY][threadX] = (float)yuvaData[srcIndex] - 512.0f; // Center around 0
    } else {
        blockData[threadY][threadX] = 0.0f;
    }

    __syncthreads();

    // Perform 2D DCT using separable 1D DCTs
    // First pass: DCT on rows
    float sum = 0.0f;
    for (int x = 0; x < 8; x++) {
        float cosVal = cosf((2.0f * x + 1.0f) * threadX * CUDART_PI_F / 16.0f);
        sum += blockData[threadY][x] * cosVal;
    }
    float alpha = (threadX == 0) ? sqrtf(1.0f / 8.0f) : sqrtf(2.0f / 8.0f);
    dctData[threadY][threadX] = alpha * sum;

    __syncthreads();

    // Second pass: DCT on columns
    sum = 0.0f;
    for (int y = 0; y < 8; y++) {
        float cosVal = cosf((2.0f * y + 1.0f) * threadY * CUDART_PI_F / 16.0f);
        sum += dctData[y][threadX] * cosVal;
    }
    alpha = (threadY == 0) ? sqrtf(1.0f / 8.0f) : sqrtf(2.0f / 8.0f);
    float finalCoeff = alpha * sum;

    // Store DCT coefficient
    int dctIndex = component * componentStride + 
                   (blockY * 8 + threadY) * width + 
                   (blockX * 8 + threadX);
    dctCoeffs[dctIndex] = (int16_t)roundf(finalCoeff);
}

// CUDA kernel for quantization
__global__ void quantizationKernel(
    int16_t* dctCoeffs,
    int16_t* quantCoeffs,
    int width,
    int height,
    int componentStride,
    int component,
    int quality
) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x >= width || y >= height) return;

    int coeffIndex = component * componentStride + y * width + x;
    int16_t coeff = dctCoeffs[coeffIndex];

    // Get quantization table entry (8x8 pattern)
    int tableX = x % 8;
    int tableY = y % 8;
    int tableIndex = tableY * 8 + tableX;
    
    uint16_t quantValue = d_quantTables[component][tableIndex];
    
    // Apply quality scaling
    quantValue = (quantValue * quality) / 4;
    quantValue = max(quantValue, (uint16_t)1);

    // Quantize coefficient
    int16_t quantizedCoeff = coeff / (int16_t)quantValue;
    quantCoeffs[coeffIndex] = quantizedCoeff;
}

// Launcher functions
extern "C" {
    void launchRgbaToYuva444_10bit(
        float* rgbaData, 
        uint16_t* yuvaData, 
        int width, 
        int height, 
        cudaStream_t stream
    ) {
        dim3 blockSize(16, 16);
        dim3 gridSize((width + blockSize.x - 1) / blockSize.x, 
                      (height + blockSize.y - 1) / blockSize.y);
        
        rgbaToYuva444_10bitKernel<<<gridSize, blockSize, 0, stream>>>(
            rgbaData, yuvaData, width, height
        );
    }

    void launchForwardDCT(
        uint16_t* yuvaData,
        int16_t* dctCoeffs,
        int width,
        int height,
        int componentStride,
        cudaStream_t stream
    ) {
        dim3 blockSize(8, 8);  // 8x8 threads per block for 8x8 DCT
        dim3 gridSize((width + 7) / 8, (height + 7) / 8);
        
        // Process each component (Y, U, V, A)
        for (int component = 0; component < 4; component++) {
            forwardDCTKernel<<<gridSize, blockSize, 0, stream>>>(
                yuvaData, dctCoeffs, width, height, componentStride, component
            );
        }
    }

    void launchQuantization(
        int16_t* dctCoeffs,
        int16_t* quantCoeffs,
        uint16_t* quantTables,
        int width,
        int height,
        int quality,
        cudaStream_t stream
    ) {
        dim3 blockSize(16, 16);
        dim3 gridSize((width + blockSize.x - 1) / blockSize.x,
                      (height + blockSize.y - 1) / blockSize.y);

        int componentStride = width * height;

        // Process each component (Y, U, V, A)
        for (int component = 0; component < 4; component++) {
            quantizationKernel<<<gridSize, blockSize, 0, stream>>>(
                dctCoeffs, quantCoeffs, width, height, componentStride, component, quality
            );
        }
    }

    // Simple run-length encoding for ProRes (placeholder for full Huffman)
    void launchHuffmanEncode(
        int16_t* quantCoeffs,
        uint8_t* compressedData,
        uint32_t* huffmanTables,
        int width,
        int height,
        size_t* outputSize,
        cudaStream_t stream
    ) {
        // For now, implement simple run-length encoding
        // In a full implementation, this would use proper ProRes Huffman tables

        // This is a simplified version - in practice, ProRes uses complex
        // variable-length coding and slice-based compression

        // Copy quantized coefficients directly (uncompressed for now)
        size_t dataSize = width * height * 4 * sizeof(int16_t); // 4 components
        cudaMemcpyAsync(compressedData, quantCoeffs, dataSize, cudaMemcpyDeviceToDevice, stream);

        // Set output size (this would be much smaller with proper compression)
        cudaMemcpyAsync(outputSize, &dataSize, sizeof(size_t), cudaMemcpyHostToDevice, stream);
    }

    // Motion estimation kernel for inter-frame compression
    void launchMotionEstimation(
        uint16_t* currentFrame,
        uint16_t* previousFrame,
        int16_t* motionVectors,
        int width,
        int height,
        int searchRange,
        cudaStream_t stream
    ) {
        // Note: ProRes is actually an intra-only codec (no inter-frame compression)
        // This is here for educational purposes and future codec development

        // For ProRes, we don't actually use motion estimation
        // ProRes uses only intra-frame compression with DCT and quantization

        // Set motion vectors to zero (no motion)
        cudaMemsetAsync(motionVectors, 0, width * height * 2 * sizeof(int16_t), stream);
    }

    // Inter-frame prediction (not used in ProRes, but included for completeness)
    void launchInterFramePrediction(
        uint16_t* currentFrame,
        uint16_t* previousFrame,
        int16_t* motionVectors,
        int16_t* residual,
        int width,
        int height,
        cudaStream_t stream
    ) {
        // Note: ProRes doesn't use inter-frame prediction
        // It's an intra-only codec, similar to Motion JPEG

        // For ProRes, residual = current frame (no prediction)
        cudaMemcpyAsync(residual, currentFrame, width * height * sizeof(uint16_t),
                       cudaMemcpyDeviceToDevice, stream);
    }
}
#endif
