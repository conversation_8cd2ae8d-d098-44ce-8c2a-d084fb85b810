#include "ImageMatterLib.h"
#include <string>
#include <memory>
#include <cstring>

// Include DirectVideoReaderAlpha
#include "../include/DirectVideoReaderAlpha.h"

// Declare the shared function (updated to new multi-pass API)
int VideoBackgroundRemoval(
    const std::wstring& inputVideo,
    const std::wstring& outputVideo,
    EngineType engine,
    ProgressCallback progressCb,
    void* userData
);

extern "C" {

int ConvertVideoToProRes4444(
    const wchar_t* inputVideo,
    const wchar_t* outputVideo,
    EngineType engine,
    ProgressCallback progressCb,
    void* userData
) {
    // Convert wchar_t* to std::wstring
    std::wstring in(inputVideo ? inputVideo : L"");
    std::wstring out(outputVideo ? outputVideo : L"");
    return VideoBackgroundRemoval(in, out, engine, progressCb, userData);
}

// DirectVideoReaderAlpha C interface implementations

VideoReaderAlphaHandle CreateVideoReaderAlpha(const wchar_t* filePath) {
    if (!filePath) return nullptr;

    // Convert wchar_t* to std::string
    std::wstring wstr(filePath);
    std::string str(wstr.begin(), wstr.end());

    try {
        auto reader = DirectVideoReaderAlpha::Create(str);
        if (reader) {
            // Return a raw pointer that we'll manage
            return new std::shared_ptr<DirectVideoReaderAlpha>(reader);
        }
    }
    catch (...) {
        // Ignore exceptions and return nullptr
    }

    return nullptr;
}

void DestroyVideoReaderAlpha(VideoReaderAlphaHandle handle) {
    if (handle) {
        auto* readerPtr = static_cast<std::shared_ptr<DirectVideoReaderAlpha>*>(handle);
        delete readerPtr;
    }
}

int GetVideoWidth(VideoReaderAlphaHandle handle) {
    if (!handle) return 0;
    auto* readerPtr = static_cast<std::shared_ptr<DirectVideoReaderAlpha>*>(handle);
    if (*readerPtr) {
        return (*readerPtr)->GetWidth();
    }
    return 0;
}

int GetVideoHeight(VideoReaderAlphaHandle handle) {
    if (!handle) return 0;
    auto* readerPtr = static_cast<std::shared_ptr<DirectVideoReaderAlpha>*>(handle);
    if (*readerPtr) {
        return (*readerPtr)->GetHeight();
    }
    return 0;
}

double GetVideoDuration(VideoReaderAlphaHandle handle) {
    if (!handle) return 0.0;
    auto* readerPtr = static_cast<std::shared_ptr<DirectVideoReaderAlpha>*>(handle);
    if (*readerPtr) {
        return (*readerPtr)->GetDuration();
    }
    return 0.0;
}

double GetVideoFrameRate(VideoReaderAlphaHandle handle) {
    if (!handle) return 0.0;
    auto* readerPtr = static_cast<std::shared_ptr<DirectVideoReaderAlpha>*>(handle);
    if (*readerPtr) {
        return (*readerPtr)->GetFrameRateDouble();
    }
    return 0.0;
}

bool GetVideoHasAlpha(VideoReaderAlphaHandle handle) {
    if (!handle) return false;
    auto* readerPtr = static_cast<std::shared_ptr<DirectVideoReaderAlpha>*>(handle);
    if (*readerPtr) {
        return (*readerPtr)->HasAlphaChannel();
    }
    return false;
}

int GetVideoRgbaBufferSize(VideoReaderAlphaHandle handle) {
    if (!handle) return 0;
    auto* readerPtr = static_cast<std::shared_ptr<DirectVideoReaderAlpha>*>(handle);
    if (*readerPtr) {
        return static_cast<int>((*readerPtr)->GetRgbaBufferSize());
    }
    return 0;
}

int GetVideoPixelFormatName(VideoReaderAlphaHandle handle, char* buffer, int bufferSize) {
    if (!handle || !buffer || bufferSize <= 0) return 0;

    auto* readerPtr = static_cast<std::shared_ptr<DirectVideoReaderAlpha>*>(handle);
    if (*readerPtr) {
        std::string pixelFormat = (*readerPtr)->GetPixelFormatName();
        int len = static_cast<int>(pixelFormat.length());

        if (bufferSize > len) {
            strcpy_s(buffer, bufferSize, pixelFormat.c_str());
        } else if (bufferSize > 0) {
            strncpy_s(buffer, bufferSize, pixelFormat.c_str(), bufferSize - 1);
            buffer[bufferSize - 1] = '\0';
        }

        return len;
    }

    return 0;
}

bool SeekVideo(VideoReaderAlphaHandle handle, double timeInSeconds) {
    if (!handle) return false;
    auto* readerPtr = static_cast<std::shared_ptr<DirectVideoReaderAlpha>*>(handle);
    if (*readerPtr) {
        return (*readerPtr)->Seek(timeInSeconds);
    }
    return false;
}

double ReadVideoFrame(VideoReaderAlphaHandle handle, void* cpuRgbaBuffer, int bufferSize) {
    if (!handle || !cpuRgbaBuffer || bufferSize <= 0) return -1.0;

    auto* readerPtr = static_cast<std::shared_ptr<DirectVideoReaderAlpha>*>(handle);
    if (*readerPtr) {
        return (*readerPtr)->ReadFrame(cpuRgbaBuffer, static_cast<size_t>(bufferSize));
    }

    return -1.0;
}

}
