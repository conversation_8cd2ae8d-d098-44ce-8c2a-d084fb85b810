# DirectVideoReaderAlpha C# Wrapper

This document describes how to use the DirectVideoReaderAlpha C# wrapper for reading ProRes 4444 videos with alpha channels and performing video background removal.

## Overview

The DirectVideoReaderAlpha C# wrapper provides a managed interface to the native C++ DirectVideoReaderAlpha library. It supports:

- CPU-based ProRes 4444 video reading with alpha channel support
- Frame-by-frame RGBA data extraction
- Video property inspection (width, height, duration, frame rate, etc.)
- Seeking to specific time positions
- Video background removal and conversion to ProRes 4444

## Requirements

- .NET Framework 4.7.2 or later / .NET Core 3.1 or later
- ImageMatterLib.dll (native library)
- FFmpeg libraries (included with ImageMatterLib.dll)

## Basic Usage

### Reading Video Properties

```csharp
using ImageMatterLib;

// Open a video file
using (var reader = new DirectVideoReaderAlpha(@"C:\path\to\video.mov"))
{
    Console.WriteLine($"Width: {reader.Width}");
    Console.WriteLine($"Height: {reader.Height}");
    Console.WriteLine($"Duration: {reader.Duration} seconds");
    Console.WriteLine($"Frame Rate: {reader.FrameRate} fps");
    Console.WriteLine($"Has Alpha: {reader.HasAlpha}");
    Console.WriteLine($"Pixel Format: {reader.PixelFormatName}");
}
```

### Reading Frames

```csharp
using (var reader = new DirectVideoReaderAlpha(@"C:\path\to\video.mov"))
{
    // Allocate buffer for RGBA frame data
    byte[] frameBuffer = new byte[reader.RgbaBufferSize];
    
    // Read frames sequentially
    while (true)
    {
        double timestamp = reader.ReadFrame(frameBuffer);
        if (timestamp < 0)
            break; // End of file or error
            
        Console.WriteLine($"Read frame at {timestamp:F3} seconds");
        
        // Process frameBuffer (RGBA data)
        // Each pixel is 4 bytes: R, G, B, A
    }
}
```

### Seeking

```csharp
using (var reader = new DirectVideoReaderAlpha(@"C:\path\to\video.mov"))
{
    // Seek to 30 seconds into the video
    if (reader.Seek(30.0))
    {
        byte[] frameBuffer = new byte[reader.RgbaBufferSize];
        double timestamp = reader.ReadFrame(frameBuffer);
        Console.WriteLine($"Frame at {timestamp:F3} seconds");
    }
}
```

### Video Background Removal

```csharp
// Convert video with background removal
ProgressCallback progressCallback = (currentFrame, totalFrames, userData) =>
{
    if (totalFrames > 0)
    {
        double progress = (double)currentFrame / totalFrames * 100.0;
        Console.WriteLine($"Progress: {progress:F1}%");
    }
    return true; // Continue processing
};

int result = DirectVideoReaderAlpha.ConvertToProRes4444(
    @"C:\input\video.mp4",
    @"C:\output\video_no_bg.mov",
    EngineType.Auto,
    false, // Single-step processing
    progressCallback);

if (result == 0)
    Console.WriteLine("Conversion successful!");
```

## API Reference

### DirectVideoReaderAlpha Class

#### Constructor
- `DirectVideoReaderAlpha(string filePath)` - Opens a video file

#### Properties
- `int Width` - Video width in pixels
- `int Height` - Video height in pixels
- `double Duration` - Video duration in seconds
- `double FrameRate` - Video frame rate
- `bool HasAlpha` - Whether video has alpha channel
- `int RgbaBufferSize` - Required buffer size for frames
- `string PixelFormatName` - Pixel format name

#### Methods
- `bool Seek(double timeInSeconds)` - Seek to time position
- `double ReadFrame(byte[] rgbaBuffer)` - Read next frame
- `static int ConvertToProRes4444(...)` - Convert video with background removal

### EngineType Enum
- `ONNX` - Use ONNX Runtime
- `TensorRT` - Use TensorRT
- `Auto` - Automatically select best engine

### ProgressCallback Delegate
```csharp
public delegate bool ProgressCallback(int currentFrame, int totalFrames, IntPtr userData);
```

## Memory Management

The wrapper automatically handles memory management:
- Native resources are freed when the object is disposed
- Use `using` statements to ensure proper cleanup
- Frame buffers are managed arrays that don't require manual cleanup

## Error Handling

The wrapper throws exceptions for common error conditions:
- `ArgumentException` - Invalid file path or parameters
- `InvalidOperationException` - Failed to open video file
- `ObjectDisposedException` - Using disposed object

## Performance Considerations

- Allocate frame buffers once and reuse them
- Use appropriate buffer sizes (check `RgbaBufferSize` property)
- Consider using unsafe code for high-performance scenarios
- The native library uses CPU-only processing (no CUDA dependencies)

## Example Applications

See `DirectVideoReaderAlphaExample.cs` for complete examples including:
- Reading video properties and frames
- Converting videos with progress tracking
- Extracting frames to files

## Troubleshooting

### Common Issues

1. **DllNotFoundException**: Ensure ImageMatterLib.dll is in the application directory or system PATH
2. **InvalidOperationException**: Check that the video file exists and is a supported format
3. **ArgumentException**: Verify buffer sizes are sufficient for frame data

### Supported Formats

The library supports formats that FFmpeg can decode, with particular focus on:
- ProRes 4444 (with alpha)
- ProRes 422
- H.264/H.265
- Other formats with alpha channel support

### Dependencies

Make sure these files are available:
- ImageMatterLib.dll
- FFmpeg libraries (avcodec, avformat, avutil, swscale)
- Visual C++ Redistributable (if required)
