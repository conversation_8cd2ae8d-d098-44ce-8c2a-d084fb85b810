﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Fichiers sources">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Fichiers d%27en-tête">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Fichiers de ressources">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\alpha_codec.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\AlphaVideoExample.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\AsyncDirectVideoReader.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\CudaProResEncoder.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\DetectHeadsStep.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\DirectVideoReader.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\DirectVideoReaderAlpha.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\DirectVideoWriter.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\DirectVideoWriterAlphaCuda.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\FinalInitialAlphaStep.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="src\FrameMetadata.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\FrameMetadataStorage.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\FrameProcessor.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\framework.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\HeadDetector.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\Helpers.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\Helpers_Heads.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\ImageMatting.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\ImageMattingFactory.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\ImageMattingOnnx.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\ImageMattingTensorRt.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\lodepng.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\ProcessBodyRegionsStep.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\ProcessHeadRegionsStep.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\ProcessingStep.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\StringUtils.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\VideoBackgroundRemoval.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
    <ClInclude Include="include\VideoWriterAlpha.h">
      <Filter>Fichiers d%27en-tête</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\alpha_codec.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\AsyncDirectVideoReader.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\CudaProResEncoder.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\DetectHeadsStep.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\DirectVideoReader.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\DirectVideoReaderAlpha.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\DirectVideoWriter.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\DirectVideoWriterAlphaCuda.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\FinalInitialAlphaStep.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\FrameMetadataStorage.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\FrameProcessor.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\HeadDetector.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\Helpers.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\Helpers_Heads.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\ImageMatting.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\ImageMattingFactory.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\ImageMattingOnnx.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\ImageMattingTensorRt.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\lodepng.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\ProcessBodyRegionsStep.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\ProcessHeadRegionsStep.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\ProcessingStep.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\VideoBackgroundRemoval.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
    <ClCompile Include="src\VideoWriterAlpha.cpp">
      <Filter>Fichiers sources</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="include\alpha_codec.cuh" />
    <None Include="include\BackgroundEstimationKernels.cuh" />
    <None Include="include\DownscaleResizingKernels.cuh" />
    <None Include="include\HeadDetectorKernels.cuh" />
    <None Include="include\Helpers_Kernels.cuh" />
    <None Include="include\main_Kernels.cuh" />
    <None Include="include\Matting_Kernels.cuh" />
    <None Include="include\UpscaleResizingKernels.cuh" />
    <None Include="src\alpha_codec.cu" />
    <None Include="src\BackgroundEstimationKernels.cu" />
    <None Include="src\CudaProResKernels.cu" />
    <None Include="src\DownscaleResizingKernels.cu" />
    <None Include="src\HeadDetectorKernels.cu" />
    <None Include="src\Helpers_Kernels.cu" />
    <None Include="src\main_Kernels.cu" />
    <None Include="src\Matting_Kernels.cu" />
    <None Include="src\UpscaleResizingKernels.cu" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="src\IMPORTANT - Principles.txt" />
  </ItemGroup>
</Project>