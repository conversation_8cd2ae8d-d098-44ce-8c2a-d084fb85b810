#include "DetectHeadsStep.h"
#include "Matting_Kernels.cuh"
#include "main_Kernels.cuh"
#include "Helpers.h"
#include <iostream>
#include <cuda_runtime.h>

DetectHeadsStep::DetectHeadsStep()
    : m_rgb<PERSON>uffer(nullptr)
    , m_rgbBufferSize(0)
    , m_width(0)
    , m_height(0)
    , m_cudaContext(nullptr)
    , m_stream(nullptr)
    , m_initialized(false) {
}

DetectHeadsStep::~DetectHeadsStep() {
    DeallocateBuffers();
}

bool DetectHeadsStep::Initialize(int width, int height, CUcontext cudaContext, cudaStream_t stream) {
    if (m_initialized) {
        std::cerr << "DetectHeadsStep already initialized" << std::endl;
        return false;
    }

    m_width = width;
    m_height = height;
    m_cudaContext = cudaContext;
    m_stream = stream;

    // Set CUDA context
    CUresult contextResult = cuCtxSetCurrent(m_cudaContext);
    if (contextResult != CUDA_SUCCESS) {
        std::cerr << "Failed to set CUDA context in DetectHeadsStep: " << contextResult << std::endl;
        return false;
    }

    // Initialize head detector
    m_headDetector = std::make_unique<HeadDetector>();
    if (!m_headDetector->Initialize(m_width, m_height, m_stream)) {
        std::cerr << "Failed to initialize head detector" << std::endl;
        return false;
    }

    // Allocate processing buffers
    if (!AllocateBuffers()) {
        std::cerr << "Failed to allocate buffers for DetectHeadsStep" << std::endl;
        return false;
    }

    m_initialized = true;
    std::cout << "DetectHeadsStep initialized successfully (" << width << "x" << height << ")" << std::endl;
    return true;
}

bool DetectHeadsStep::Process(void* inputNv12Data, size_t inputPitch, FrameMetadata& frameMetadata) {
    if (!m_initialized) {
        std::cerr << "DetectHeadsStep not initialized" << std::endl;
        return false;
    }

    // Set CUDA context for this thread
    CUresult contextResult = cuCtxSetCurrent(m_cudaContext);
    if (contextResult != CUDA_SUCCESS) {
        std::cerr << "Failed to set CUDA context in DetectHeadsStep::Process: " << contextResult << std::endl;
        return false;
    }

    try {
        // Convert NV12 to RGB for head detection
        launchByteNv12ToPlanarFloatRgb(
            static_cast<unsigned char*>(inputNv12Data),
            m_rgbBuffer,
            m_width,
            m_height,
            inputPitch,
            m_stream
        );

        CUDA_CHECK(cudaStreamSynchronize(m_stream));

        // Clear previous detection results
        m_detectionResults.clear();

        // Run head detection
        if (!m_headDetector->DetectHeads(m_rgbBuffer, m_width, m_height,
                                        m_detectionResults, 0.5f)) {
            std::cerr << "Failed to detect heads" << std::endl;
            return false;
        }

        // Store detection results in frame metadata
        if (!frameMetadata.SetHeadDetections(m_detectionResults)) {
            std::cerr << "Failed to store head detection results in frame metadata" << std::endl;
            return false;
        }

        // Mark this step as completed
        frameMetadata.hasHeadDetection = true;

        std::cout << "DetectHeadsStep: Found " << m_detectionResults.size() << " heads" << std::endl;

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in DetectHeadsStep::Process: " << e.what() << std::endl;
        return false;
    }
}

bool DetectHeadsStep::AllocateBuffers() {
    try {
        // Allocate RGB buffer for head detection input
        m_rgbBufferSize = m_width * m_height * 3 * sizeof(float);
        CUDA_CHECK(cudaMalloc(&m_rgbBuffer, m_rgbBufferSize));

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Failed to allocate buffers: " << e.what() << std::endl;
        DeallocateBuffers();
        return false;
    }
}

void DetectHeadsStep::DeallocateBuffers() {
    if (m_rgbBuffer) {
        cudaFree(m_rgbBuffer);
        m_rgbBuffer = nullptr;
    }

    m_rgbBufferSize = 0;
}
