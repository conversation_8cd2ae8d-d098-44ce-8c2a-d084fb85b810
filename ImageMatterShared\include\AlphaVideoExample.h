// AlphaVideoExample.h
#pragma once

#include "DirectVideoReaderAlpha.h"
#include "DirectVideoWriterAlpha.h"
#include <memory>
#include <string>

/**
 * Example usage of DirectVideoReaderAlpha and DirectVideoWriterAlpha
 * 
 * This header demonstrates how to use the new alpha video modules
 * for reading and writing video files with alpha channel support.
 */

namespace AlphaVideoExample {

    /**
     * Example: Read a ProRes 4444 file with alpha and write to another ProRes 4444 file
     * @param inputPath Path to input video file with alpha (e.g., ProRes 4444)
     * @param outputPath Path to output video file
     * @param cudaContext CUDA context to use for operations
     * @return true on success, false on failure
     */
    bool ProcessAlphaVideo(const std::string& inputPath, const std::string& outputPath, CUcontext cudaContext) {
        try {
            // Create alpha video reader
            auto reader = DirectVideoReaderAlpha::Create(inputPath, cudaContext, false);
            if (!reader) {
                std::cerr << "Failed to create alpha video reader" << std::endl;
                return false;
            }

            std::cout << "Input video properties:" << std::endl;
            std::cout << "  Dimensions: " << reader->GetWidth() << "x" << reader->GetHeight() << std::endl;
            std::cout << "  Duration: " << reader->GetDuration() << " seconds" << std::endl;
            std::cout << "  Frame rate: " << reader->GetFrameRateDouble() << " fps" << std::endl;
            std::cout << "  Has alpha: " << (reader->HasAlphaChannel() ? "Yes" : "No") << std::endl;
            std::cout << "  Pixel format: " << reader->GetPixelFormatName() << std::endl;

            // Configure output for ProRes 4444
            DirectVideoWriterAlpha::OutputConfig outputConfig;
            outputConfig.width = reader->GetWidth();
            outputConfig.height = reader->GetHeight();
            outputConfig.frameRate = reader->GetFrameRate();
            outputConfig.UseProRes4444(); // Use ProRes 4444 with alpha
            outputConfig.bitRate = 100000000; // 100 Mbps for high quality

            // Create alpha video writer
            std::wstring outputPathW = std::wstring(outputPath.begin(), outputPath.end());
            auto writer = DirectVideoWriterAlpha::Create(outputPathW, outputConfig, cudaContext);
            if (!writer) {
                std::cerr << "Failed to create alpha video writer" << std::endl;
                return false;
            }

            // Allocate CUDA buffer for RGBA frames
            size_t rgbaBufferSize = reader->GetWidth() * reader->GetHeight() * 4 * sizeof(uint8_t);
            void* cudaRgbaBuffer = nullptr;
            cudaMalloc(&cudaRgbaBuffer, rgbaBufferSize);

            std::cout << "Processing frames..." << std::endl;
            int frameCount = 0;
            double timestamp = 0.0;

            // Process all frames
            while ((timestamp = reader->ReadFrame(cudaRgbaBuffer, rgbaBufferSize)) >= 0.0) {
                // Here you could process the RGBA frame data if needed
                // For this example, we just pass it through

                if (!writer->WriteFrame(cudaRgbaBuffer, rgbaBufferSize)) {
                    std::cerr << "Failed to write frame " << frameCount << std::endl;
                    break;
                }

                frameCount++;
                if (frameCount % 30 == 0) {
                    std::cout << "Processed " << frameCount << " frames (timestamp: " << timestamp << "s)" << std::endl;
                }
            }

            std::cout << "Finalizing video..." << std::endl;
            writer->Finalize();
            writer->Close();

            // Cleanup
            cudaFree(cudaRgbaBuffer);

            std::cout << "Successfully processed " << frameCount << " frames" << std::endl;
            return true;
        }
        catch (const std::exception& e) {
            std::cerr << "Error processing alpha video: " << e.what() << std::endl;
            return false;
        }
    }

    /**
     * Example: Convert a regular video to ProRes 4444 with synthetic alpha
     * @param inputPath Path to input video file (any format)
     * @param outputPath Path to output ProRes 4444 file
     * @param cudaContext CUDA context to use for operations
     * @return true on success, false on failure
     */
    bool AddAlphaToVideo(const std::string& inputPath, const std::string& outputPath, CUcontext cudaContext) {
        // This would use the regular DirectVideoReader to read RGB data
        // and then add a synthetic alpha channel before writing with DirectVideoWriterAlpha
        // Implementation would be similar to ProcessAlphaVideo but with alpha generation
        return false; // Placeholder
    }

    /**
     * Example configuration for different alpha video formats
     */
    namespace Configs {
        
        // ProRes 4444 configuration (highest quality with alpha)
        DirectVideoWriterAlpha::OutputConfig GetProRes4444Config(int width, int height, AVRational frameRate) {
            DirectVideoWriterAlpha::OutputConfig config;
            config.width = width;
            config.height = height;
            config.frameRate = frameRate;
            config.UseProRes4444();
            config.bitRate = 200000000; // 200 Mbps for very high quality
            return config;
        }

        // ProRes 4444 XQ configuration (extreme quality with alpha)
        DirectVideoWriterAlpha::OutputConfig GetProRes4444XQConfig(int width, int height, AVRational frameRate) {
            DirectVideoWriterAlpha::OutputConfig config;
            config.width = width;
            config.height = height;
            config.frameRate = frameRate;
            config.UseProRes4444XQ();
            config.bitRate = 500000000; // 500 Mbps for extreme quality
            return config;
        }

        // FFV1 lossless configuration with alpha
        DirectVideoWriterAlpha::OutputConfig GetFFV1AlphaConfig(int width, int height, AVRational frameRate) {
            DirectVideoWriterAlpha::OutputConfig config;
            config.width = width;
            config.height = height;
            config.frameRate = frameRate;
            config.UseFFV1Alpha();
            config.bitRate = 0; // Lossless doesn't use bitrate
            return config;
        }

        // UTVideo lossless configuration with alpha
        DirectVideoWriterAlpha::OutputConfig GetUTVideoAlphaConfig(int width, int height, AVRational frameRate) {
            DirectVideoWriterAlpha::OutputConfig config;
            config.width = width;
            config.height = height;
            config.frameRate = frameRate;
            config.UseUTVideoAlpha();
            config.bitRate = 0; // Lossless doesn't use bitrate
            return config;
        }
    }

} // namespace AlphaVideoExample
