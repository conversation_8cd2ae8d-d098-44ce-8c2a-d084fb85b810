﻿     Création de la bibliothèque F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Debug\ImageMatterApp.lib et de l'objet F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Debug\ImageMatterApp.exp
ImageMatterShared.lib(FrameProcessor.obj) : error LNK2019: symbole externe non résolu launchEstimateBackgroundHorizontal référencé dans la fonction "public: bool __cdecl FrameProcessor::ProcessFrame(void *,unsigned __int64,float *)" (?ProcessFrame@FrameProcessor@@QEAA_NPEAX_KPEAM@Z)
ImageMatterShared.lib(FrameProcessor.obj) : error LNK2019: symbole externe non résolu launchEstimateBackgroundVertical référencé dans la fonction "public: bool __cdecl FrameProcessor::ProcessFrame(void *,unsigned __int64,float *)" (?ProcessFrame@FrameProcessor@@QEAA_NPEAX_KPEAM@Z)
ImageMatterShared.lib(FrameProcessor.obj) : error LNK2019: symbole externe non résolu launchExtractForeground référencé dans la fonction "public: bool __cdecl FrameProcessor::ProcessFrame(void *,unsigned __int64,float *)" (?ProcessFrame@FrameProcessor@@QEAA_NPEAX_KPEAM@Z)
ImageMatterShared.lib(ProcessHeadRegionsStep.obj) : error LNK2001: symbole externe non résolu "void __cdecl launchByteNv12ToPlanarFloatRgb(unsigned char *,float *,int,int,unsigned __int64,struct CUstream_st *)" (?launchByteNv12ToPlanarFloatRgb@@YAXPEAEPEAMHH_KPEAUCUstream_st@@@Z)
ImageMatterShared.lib(FrameProcessor.obj) : error LNK2001: symbole externe non résolu "void __cdecl launchByteNv12ToPlanarFloatRgb(unsigned char *,float *,int,int,unsigned __int64,struct CUstream_st *)" (?launchByteNv12ToPlanarFloatRgb@@YAXPEAEPEAMHH_KPEAUCUstream_st@@@Z)
ImageMatterShared.lib(ProcessBodyRegionsStep.obj) : error LNK2001: symbole externe non résolu "void __cdecl launchByteNv12ToPlanarFloatRgb(unsigned char *,float *,int,int,unsigned __int64,struct CUstream_st *)" (?launchByteNv12ToPlanarFloatRgb@@YAXPEAEPEAMHH_KPEAUCUstream_st@@@Z)
ImageMatterShared.lib(FinalInitialAlphaStep.obj) : error LNK2001: symbole externe non résolu "void __cdecl launchByteNv12ToPlanarFloatRgb(unsigned char *,float *,int,int,unsigned __int64,struct CUstream_st *)" (?launchByteNv12ToPlanarFloatRgb@@YAXPEAEPEAMHH_KPEAUCUstream_st@@@Z)
ImageMatterShared.lib(DetectHeadsStep.obj) : error LNK2001: symbole externe non résolu "void __cdecl launchByteNv12ToPlanarFloatRgb(unsigned char *,float *,int,int,unsigned __int64,struct CUstream_st *)" (?launchByteNv12ToPlanarFloatRgb@@YAXPEAEPEAMHH_KPEAUCUstream_st@@@Z)
ImageMatterShared.lib(FrameProcessor.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchFloatToUint8Alpha(float *,unsigned char *,int,int,struct CUstream_st *)" (?launchFloatToUint8Alpha@@YAXPEAMPEAEHHPEAUCUstream_st@@@Z) référencé dans la fonction "public: bool __cdecl FrameProcessor::FindInitialAlpha(void *,unsigned __int64,unsigned char * *,unsigned __int64 *)" (?FindInitialAlpha@FrameProcessor@@QEAA_NPEAX_KPEAPEAEPEA_K@Z)
ImageMatterShared.lib(FrameProcessor.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchUint8ToFloatAlpha(unsigned char *,float *,int,int,struct CUstream_st *)" (?launchUint8ToFloatAlpha@@YAXPEAEPEAMHHPEAUCUstream_st@@@Z) référencé dans la fonction "public: bool __cdecl FrameProcessor::RefineInitialAlpha(void *,unsigned __int64,unsigned char *,unsigned __int64,float *)" (?RefineInitialAlpha@FrameProcessor@@QEAA_NPEAX_KPEAE1PEAM@Z)
ImageMatterShared.lib(FrameProcessor.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchDetectUncertainRegions(float *,unsigned char *,int,int,int,struct CUstream_st *)" (?launchDetectUncertainRegions@@YAXPEAMPEAEHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl FrameProcessor::ProcessBodyRegions(void)" (?ProcessBodyRegions@FrameProcessor@@AEAAXXZ)
ImageMatterShared.lib(ProcessBodyRegionsStep.obj) : error LNK2001: symbole externe non résolu "void __cdecl launchDetectUncertainRegions(float *,unsigned char *,int,int,int,struct CUstream_st *)" (?launchDetectUncertainRegions@@YAXPEAMPEAEHHHPEAUCUstream_st@@@Z)
ImageMatterShared.lib(FrameProcessor.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchPrepareIndexNetInput(float *,float const *,float const *,int,int,int,int,int,int,int,struct CUstream_st *)" (?launchPrepareIndexNetInput@@YAXPEAMPEBM1HHHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl FrameProcessor::ProcessBodyRegions(void)" (?ProcessBodyRegions@FrameProcessor@@AEAAXXZ)
ImageMatterShared.lib(FrameProcessor.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchUpdateAlphaBufferRegion(float *,unsigned __int64,float const *,float const *,int,int,int,int,int,int,struct CUstream_st *)" (?launchUpdateAlphaBufferRegion@@YAXPEAM_KPEBM2HHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl FrameProcessor::ProcessBodyRegions(void)" (?ProcessBodyRegions@FrameProcessor@@AEAAXXZ)
ImageMatterShared.lib(FrameProcessor.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchGenerateTrimap(float *,float const *,int,int,struct CUstream_st *)" (?launchGenerateTrimap@@YAXPEAMPEBMHHPEAUCUstream_st@@@Z) référencé dans la fonction "public: bool __cdecl FrameProcessor::ProcessFrame(void *,unsigned __int64,float *)" (?ProcessFrame@FrameProcessor@@QEAA_NPEAX_KPEAM@Z)
ImageMatterShared.lib(ProcessBodyRegionsStep.obj) : error LNK2001: symbole externe non résolu "void __cdecl launchGenerateTrimap(float *,float const *,int,int,struct CUstream_st *)" (?launchGenerateTrimap@@YAXPEAMPEBMHHPEAUCUstream_st@@@Z)
ImageMatterShared.lib(ProcessHeadRegionsStep.obj) : error LNK2001: symbole externe non résolu "void __cdecl launchGenerateTrimap(float *,float const *,int,int,struct CUstream_st *)" (?launchGenerateTrimap@@YAXPEAMPEBMHHPEAUCUstream_st@@@Z)
ImageMatterShared.lib(FrameProcessor.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchExtractHeadRegion(float const *,float *,int,int,int,int,int,int,struct CUstream_st *)" (?launchExtractHeadRegion@@YAXPEBMPEAMHHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl FrameProcessor::ProcessHeadRegions(void)" (?ProcessHeadRegions@FrameProcessor@@AEAAXXZ)
ImageMatterShared.lib(FrameProcessor.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchUpdateAlphaFromHeadRegion(float *,float const *,float const *,int,int,int,int,int,int,struct CUstream_st *)" (?launchUpdateAlphaFromHeadRegion@@YAXPEAMPEBM1HHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl FrameProcessor::ProcessHeadRegions(void)" (?ProcessHeadRegions@FrameProcessor@@AEAAXXZ)
ImageMatterShared.lib(FrameProcessor.obj) : error LNK2019: symbole externe non résolu launchRescanHeadRegions référencé dans la fonction "public: bool __cdecl FrameProcessor::ProcessFrame(void *,unsigned __int64,float *)" (?ProcessFrame@FrameProcessor@@QEAA_NPEAX_KPEAM@Z)
ImageMatterShared.lib(DirectVideoReader.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchYuv420pToNv12(unsigned char *,unsigned char *,int,int,int,int,int,int,struct CUstream_st *)" (?launchYuv420pToNv12@@YAXPEAE0HHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: bool __cdecl DirectVideoReader::TransferFrameToCuda(void *,unsigned __int64,unsigned __int64)" (?TransferFrameToCuda@DirectVideoReader@@AEAA_NPEAX_K1@Z)
ImageMatterShared.lib(ProcessBodyRegionsStep.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchEstimateBackgroundHorizontal(float const *,float const *,float *,int,int,struct CUstream_st *)" (?launchEstimateBackgroundHorizontal@@YAXPEBM0PEAMHHPEAUCUstream_st@@@Z) référencé dans la fonction "public: bool __cdecl ProcessBodyRegionsStep::GenerateFinalOutput(void *,unsigned __int64,struct FrameMetadata const &,float *)" (?GenerateFinalOutput@ProcessBodyRegionsStep@@QEAA_NPEAX_KAEBUFrameMetadata@@PEAM@Z)
ImageMatterShared.lib(ProcessBodyRegionsStep.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchEstimateBackgroundVertical(float const *,float const *,float *,int,int,struct CUstream_st *)" (?launchEstimateBackgroundVertical@@YAXPEBM0PEAMHHPEAUCUstream_st@@@Z) référencé dans la fonction "public: bool __cdecl ProcessBodyRegionsStep::GenerateFinalOutput(void *,unsigned __int64,struct FrameMetadata const &,float *)" (?GenerateFinalOutput@ProcessBodyRegionsStep@@QEAA_NPEAX_KAEBUFrameMetadata@@PEAM@Z)
ImageMatterShared.lib(ProcessBodyRegionsStep.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchExtractForeground(float const *,float const *,float const *,float const *,float *,int,int,struct CUstream_st *)" (?launchExtractForeground@@YAXPEBM000PEAMHHPEAUCUstream_st@@@Z) référencé dans la fonction "public: bool __cdecl ProcessBodyRegionsStep::GenerateFinalOutput(void *,unsigned __int64,struct FrameMetadata const &,float *)" (?GenerateFinalOutput@ProcessBodyRegionsStep@@QEAA_NPEAX_KAEBUFrameMetadata@@PEAM@Z)
ImageMatterShared.lib(HeadDetector.obj) : error LNK2019: symbole externe non résolu LaunchLanczosResizeAndPadKernel référencé dans la fonction "public: bool __cdecl HeadDetector::DetectHeads(float *,int,int,class std::vector<struct Box,class std::allocator<struct Box> > &,float)" (?DetectHeads@HeadDetector@@QEAA_NPEAMHHAEAV?$vector@UBox@@V?$allocator@UBox@@@std@@@std@@M@Z)
ImageMatterShared.lib(ImageMatting.obj) : error LNK2001: symbole externe non résolu LaunchLanczosResizeAndPadKernel
ImageMatterShared.lib(alpha_codec.obj) : error LNK2019: symbole externe non résolu launch_compress_columns référencé dans la fonction compress_alpha_cuda
ImageMatterShared.lib(alpha_codec.obj) : error LNK2019: symbole externe non résolu launch_add_column_headers référencé dans la fonction compress_alpha_cuda
ImageMatterShared.lib(alpha_codec.obj) : error LNK2019: symbole externe non résolu launch_compress_lines référencé dans la fonction compress_alpha_cuda
ImageMatterShared.lib(alpha_codec.obj) : error LNK2019: symbole externe non résolu launch_decompress référencé dans la fonction decompress_alpha_cuda
ImageMatterShared.lib(CudaProResEncoder.obj) : error LNK2019: symbole externe non résolu launchRgbaToYuva444_10bit référencé dans la fonction "public: bool __cdecl CudaProResEncoder::WriteFrame(void *,unsigned __int64)" (?WriteFrame@CudaProResEncoder@@QEAA_NPEAX_K@Z)
ImageMatterShared.lib(ProcessHeadRegionsStep.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchExtractRegion(float const *,float *,int,int,int,int,int,int,int,struct CUstream_st *)" (?launchExtractRegion@@YAXPEBMPEAMHHHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl ProcessHeadRegionsStep::ProcessHeadRegionsImpl(void)" (?ProcessHeadRegionsImpl@ProcessHeadRegionsStep@@AEAAXXZ)
ImageMatterShared.lib(ProcessHeadRegionsStep.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchBlendAlphaRegion(float *,float const *,int,int,int,int,int,int,struct CUstream_st *)" (?launchBlendAlphaRegion@@YAXPEAMPEBMHHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl ProcessHeadRegionsStep::ProcessHeadRegionsImpl(void)" (?ProcessHeadRegionsImpl@ProcessHeadRegionsStep@@AEAAXXZ)
ImageMatterShared.lib(ProcessHeadRegionsStep.obj) : error LNK2019: symbole externe non résolu "void __cdecl launchResizeImage(float const *,float *,int,int,int,int,int,struct CUstream_st *)" (?launchResizeImage@@YAXPEBMPEAMHHHHHPEAUCUstream_st@@@Z) référencé dans la fonction "private: void __cdecl ProcessHeadRegionsStep::ProcessHeadRegionsImpl(void)" (?ProcessHeadRegionsImpl@ProcessHeadRegionsStep@@AEAAXXZ)
ImageMatterShared.lib(ImageMatting.obj) : error LNK2019: symbole externe non résolu "enum cudaError __cdecl LaunchPreprocessBufferKernel(float *,float const *,int,int,bool,struct NormalizationParams const &,struct CUstream_st *)" (?LaunchPreprocessBufferKernel@@YA?AW4cudaError@@PEAMPEBMHH_NAEBUNormalizationParams@@PEAUCUstream_st@@@Z) référencé dans la fonction "protected: enum cudaError __cdecl ImageMatting::PreprocessInputBufferCommon(float const *,float *,float *,int,int,int,int,bool,struct NormalizationParams const &,unsigned __int64,enum ResizeMethod,struct CUstream_st *)" (?PreprocessInputBufferCommon@ImageMatting@@IEAA?AW4cudaError@@PEBMPEAM1HHHH_NAEBUNormalizationParams@@_KW4ResizeMethod@@PEAUCUstream_st@@@Z)
ImageMatterShared.lib(ImageMatting.obj) : error LNK2019: symbole externe non résolu LanczosResizeKernelLauncher référencé dans la fonction "protected: enum cudaError __cdecl ImageMatting::PreprocessInputBufferCommon(float const *,float *,float *,int,int,int,int,bool,struct NormalizationParams const &,unsigned __int64,enum ResizeMethod,struct CUstream_st *)" (?PreprocessInputBufferCommon@ImageMatting@@IEAA?AW4cudaError@@PEBMPEAM1HHHH_NAEBUNormalizationParams@@_KW4ResizeMethod@@PEAUCUstream_st@@@Z)
ImageMatterShared.lib(ImageMatting.obj) : error LNK2019: symbole externe non résolu LanczosUpscaleKernelLauncher référencé dans la fonction "protected: enum cudaError __cdecl ImageMatting::PostprocessOutputBufferCommon(float const *,float *,int,int,int,int,enum ResizeMethod,struct CUstream_st *)" (?PostprocessOutputBufferCommon@ImageMatting@@IEAA?AW4cudaError@@PEBMPEAMHHHHW4ResizeMethod@@PEAUCUstream_st@@@Z)
F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Debug\ImageMatterApp.exe : fatal error LNK1120: 29 externes non résolus
