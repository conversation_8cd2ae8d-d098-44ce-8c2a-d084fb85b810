using System;
using System.IO;

namespace ImageMatterLib.Examples
{
    /// <summary>
    /// Example usage of DirectVideoReaderAlpha C# wrapper
    /// </summary>
    public class DirectVideoReaderAlphaExample
    {
        /// <summary>
        /// Example: Read video properties and first few frames
        /// </summary>
        /// <param name="videoPath">Path to the video file</param>
        public static void ReadVideoExample(string videoPath)
        {
            try
            {
                using (var reader = new DirectVideoReaderAlpha(videoPath))
                {
                    // Display video properties
                    Console.WriteLine($"Video Properties:");
                    Console.WriteLine($"  Width: {reader.Width}");
                    Console.WriteLine($"  Height: {reader.Height}");
                    Console.WriteLine($"  Duration: {reader.Duration:F2} seconds");
                    Console.WriteLine($"  Frame Rate: {reader.FrameRate:F2} fps");
                    Console.WriteLine($"  Has Alpha: {reader.HasAlpha}");
                    Console.WriteLine($"  Pixel Format: {reader.PixelFormatName}");
                    Console.WriteLine($"  RGBA Buffer Size: {reader.RgbaBufferSize} bytes");

                    // Allocate buffer for frame data
                    byte[] frameBuffer = new byte[reader.RgbaBufferSize];

                    // Read first 10 frames
                    Console.WriteLine("\nReading first 10 frames:");
                    for (int i = 0; i < 10; i++)
                    {
                        double timestamp = reader.ReadFrame(frameBuffer);
                        if (timestamp < 0)
                        {
                            Console.WriteLine($"  Frame {i}: End of file or error");
                            break;
                        }
                        Console.WriteLine($"  Frame {i}: timestamp = {timestamp:F3}s");
                    }

                    // Seek to middle of video and read a frame
                    double middleTime = reader.Duration / 2.0;
                    if (reader.Seek(middleTime))
                    {
                        double timestamp = reader.ReadFrame(frameBuffer);
                        Console.WriteLine($"\nSeeked to {middleTime:F2}s, read frame at {timestamp:F3}s");
                    }
                    else
                    {
                        Console.WriteLine($"\nFailed to seek to {middleTime:F2}s");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Convert video to ProRes 4444 with progress tracking
        /// </summary>
        /// <param name="inputPath">Input video path</param>
        /// <param name="outputPath">Output video path</param>
        public static void ConvertVideoExample(string inputPath, string outputPath)
        {
            try
            {
                Console.WriteLine($"Converting {inputPath} to {outputPath}...");

                // Progress callback
                ProgressCallback progressCallback = (currentFrame, totalFrames, userData) =>
                {
                    if (totalFrames > 0)
                    {
                        double progress = (double)currentFrame / totalFrames * 100.0;
                        Console.WriteLine($"Progress: {progress:F1}% ({currentFrame}/{totalFrames})");
                    }
                    else
                    {
                        Console.WriteLine($"Processing frame {currentFrame}...");
                    }
                    return true; // Continue processing
                };

                int result = DirectVideoReaderAlpha.ConvertToProRes4444(
                    inputPath, 
                    outputPath, 
                    EngineType.Auto, 
                    false, // Single-step processing
                    progressCallback);

                if (result == 0)
                {
                    Console.WriteLine("Conversion completed successfully!");
                }
                else
                {
                    Console.WriteLine($"Conversion failed with error code: {result}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Example: Extract frames to individual image files
        /// </summary>
        /// <param name="videoPath">Path to the video file</param>
        /// <param name="outputDir">Directory to save frames</param>
        /// <param name="maxFrames">Maximum number of frames to extract</param>
        public static void ExtractFramesExample(string videoPath, string outputDir, int maxFrames = 100)
        {
            try
            {
                Directory.CreateDirectory(outputDir);

                using (var reader = new DirectVideoReaderAlpha(videoPath))
                {
                    Console.WriteLine($"Extracting up to {maxFrames} frames from {videoPath}...");
                    
                    byte[] frameBuffer = new byte[reader.RgbaBufferSize];
                    int frameCount = 0;

                    while (frameCount < maxFrames)
                    {
                        double timestamp = reader.ReadFrame(frameBuffer);
                        if (timestamp < 0)
                        {
                            Console.WriteLine("End of video reached.");
                            break;
                        }

                        // Here you would typically save the RGBA data as an image file
                        // For this example, we'll just log the frame info
                        string framePath = Path.Combine(outputDir, $"frame_{frameCount:D6}_{timestamp:F3}s.rgba");
                        
                        // Save raw RGBA data (you might want to convert to PNG/JPEG instead)
                        File.WriteAllBytes(framePath, frameBuffer);
                        
                        Console.WriteLine($"Saved frame {frameCount}: {framePath}");
                        frameCount++;
                    }

                    Console.WriteLine($"Extracted {frameCount} frames to {outputDir}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Main example entry point
        /// </summary>
        /// <param name="args">Command line arguments</param>
        public static void Main(string[] args)
        {
            if (args.Length == 0)
            {
                Console.WriteLine("Usage:");
                Console.WriteLine("  DirectVideoReaderAlphaExample.exe <video_file>");
                Console.WriteLine("  DirectVideoReaderAlphaExample.exe <input_video> <output_video>");
                Console.WriteLine("  DirectVideoReaderAlphaExample.exe extract <video_file> <output_dir>");
                return;
            }

            if (args.Length == 1)
            {
                // Read video properties and frames
                ReadVideoExample(args[0]);
            }
            else if (args.Length == 2)
            {
                // Convert video
                ConvertVideoExample(args[0], args[1]);
            }
            else if (args.Length == 3 && args[0].ToLower() == "extract")
            {
                // Extract frames
                ExtractFramesExample(args[1], args[2]);
            }
            else
            {
                Console.WriteLine("Invalid arguments. See usage above.");
            }
        }
    }
}
