{"version": "2.0.0", "tasks": [{"label": "Build Debug", "type": "shell", "command": "msbuild", "args": ["${workspaceFolder}/ImageMatter.sln", "/p:Configuration=Debug", "/p:Platform=x64", "/v:minimal", "/m"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "Build Release", "type": "shell", "command": "msbuild", "args": ["${workspaceFolder}/ImageMatter.sln", "/p:Configuration=Release", "/p:Platform=x64", "/v:minimal", "/m"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "Build and Copy Debug", "dependsOrder": "sequence", "dependsOn": ["Build Debug", "Post-Build Debug"]}, {"label": "Build and Copy Release", "dependsOrder": "sequence", "dependsOn": ["Build Release", "Post-Build Release"]}, {"label": "Post-Build Debug", "type": "shell", "command": "cmd", "args": ["/c", "if not exist \"${workspaceFolder}\\x64\\Debug\\ModelsVggHeadDetector\" mkdir \"${workspaceFolder}\\x64\\Debug\\ModelsVggHeadDetector\" && if not exist \"${workspaceFolder}\\x64\\Debug\\ModelsMatting\" mkdir \"${workspaceFolder}\\x64\\Debug\\ModelsMatting\" && xcopy /E /I /Y /D \"%VccLibs%\\onnxruntime\\lib\\*.dll\" \"${workspaceFolder}\\x64\\Debug\\\" && xcopy /E /I /Y /D \"%VccLibs%\\ffmpeg\\bin\\*.dll\" \"${workspaceFolder}\\x64\\Debug\\\" && xcopy /E /I /Y /D \"%VccLibs%\\TensorRT\\lib\\*.dll\" \"${workspaceFolder}\\x64\\Debug\\\" && xcopy /E /I /Y /D \"%VccLibs%\\TensorRT\\bin\\*.exe\" \"${workspaceFolder}\\x64\\Debug\\\" && xcopy /E /I /Y /D \"${workspaceFolder}\\ModelsVggHeadDetector\\*.onnx\" \"${workspaceFolder}\\x64\\Debug\\ModelsVggHeadDetector\\\" && xcopy /E /I /Y /D \"${workspaceFolder}\\ModelsMatting\\*.*\" \"${workspaceFolder}\\x64\\Debug\\ModelsMatting\\\""], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "runOptions": {"runOn": "folderOpen"}}, {"label": "Post-Build Release", "type": "shell", "command": "cmd", "args": ["/c", "if not exist \"${workspaceFolder}\\x64\\Release\\ModelsVggHeadDetector\" mkdir \"${workspaceFolder}\\x64\\Release\\ModelsVggHeadDetector\" && if not exist \"${workspaceFolder}\\x64\\Release\\ModelsMatting\" mkdir \"${workspaceFolder}\\x64\\Release\\ModelsMatting\" && xcopy /E /I /Y /D \"%VccLibs%\\onnxruntime\\lib\\*.dll\" \"${workspaceFolder}\\x64\\Release\\\" && xcopy /E /I /Y /D \"%VccLibs%\\ffmpeg\\bin\\*.dll\" \"${workspaceFolder}\\x64\\Release\\\" && xcopy /E /I /Y /D \"%VccLibs%\\TensorRT\\lib\\*.dll\" \"${workspaceFolder}\\x64\\Release\\\" && xcopy /E /I /Y /D \"%VccLibs%\\TensorRT\\bin\\*.exe\" \"${workspaceFolder}\\x64\\Release\\\" && xcopy /E /I /Y /D \"${workspaceFolder}\\ModelsVggHeadDetector\\*.onnx\" \"${workspaceFolder}\\x64\\Release\\ModelsVggHeadDetector\\\" && xcopy /E /I /Y /D \"${workspaceFolder}\\ModelsMatting\\*.*\" \"${workspaceFolder}\\x64\\Release\\ModelsMatting\\\""], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}}, {"label": "Run Debug", "type": "shell", "command": "${workspaceFolder}/x64/Debug/ImageMatter.exe", "args": [], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "dependsOn": "Build and Copy Debug", "options": {"cwd": "${workspaceFolder}/x64/Debug"}}, {"label": "Run Release", "type": "shell", "command": "${workspaceFolder}/x64/Release/ImageMatter.exe", "args": [], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "dependsOn": "Build and Copy Release", "options": {"cwd": "${workspaceFolder}/x64/Release"}}, {"label": "Clean Debug", "type": "shell", "command": "msbuild", "args": ["${workspaceFolder}/ImageMatter.sln", "/p:Configuration=Debug", "/p:Platform=x64", "/t:Clean", "/v:minimal"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "Clean Release", "type": "shell", "command": "msbuild", "args": ["${workspaceFolder}/ImageMatter.sln", "/p:Configuration=Release", "/p:Platform=x64", "/t:Clean", "/v:minimal"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "Rebuild Debug", "type": "shell", "command": "msbuild", "args": ["${workspaceFolder}/ImageMatter.sln", "/p:Configuration=Debug", "/p:Platform=x64", "/t:Rebuild", "/v:minimal", "/m"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "Rebuild Release", "type": "shell", "command": "msbuild", "args": ["${workspaceFolder}/ImageMatter.sln", "/p:Configuration=Release", "/p:Platform=x64", "/t:Rebuild", "/v:minimal", "/m"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}]}