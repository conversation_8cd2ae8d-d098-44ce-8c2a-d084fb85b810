{"version": "2.0.0", "tasks": [{"label": "Build All (Debug)", "type": "shell", "command": "msbuild", "args": ["ImageMatter.sln", "/p:Configuration=Debug", "/p:Platform=x64", "/m"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile", "dependsOrder": "sequence", "dependsOn": ["Post-Build Events (Debug)"]}, {"label": "Build All (Release)", "type": "shell", "command": "msbuild", "args": ["ImageMatter.sln", "/p:Configuration=Release", "/p:Platform=x64", "/m"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile", "dependsOrder": "sequence", "dependsOn": ["Post-Build Events (Release)"]}, {"label": "Build ImageMatterShared (Debug)", "type": "shell", "command": "msbuild", "args": ["ImageMatterShared/ImageMatterShared.vcxproj", "/p:Configuration=Debug", "/p:Platform=x64"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "Build ImageMatterShared (Release)", "type": "shell", "command": "msbuild", "args": ["ImageMatterShared/ImageMatterShared.vcxproj", "/p:Configuration=Release", "/p:Platform=x64"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "Build ImageMatterLib (Debug)", "type": "shell", "command": "msbuild", "args": ["ImageMatterLib/ImageMatterLib.vcxproj", "/p:Configuration=Debug", "/p:Platform=x64"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile", "dependsOn": ["Build ImageMatterShared (Debug)"]}, {"label": "Build ImageMatterLib (Release)", "type": "shell", "command": "msbuild", "args": ["ImageMatterLib/ImageMatterLib.vcxproj", "/p:Configuration=Release", "/p:Platform=x64"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile", "dependsOn": ["Build ImageMatterShared (Release)"]}, {"label": "Build ImageMatterApp (Debug)", "type": "shell", "command": "msbuild", "args": ["ImageMatterApp/ImageMatterApp.vcxproj", "/p:Configuration=Debug", "/p:Platform=x64"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile", "dependsOn": ["Build ImageMatterShared (Debug)"]}, {"label": "Build ImageMatterApp (Release)", "type": "shell", "command": "msbuild", "args": ["ImageMatterApp/ImageMatterApp.vcxproj", "/p:Configuration=Release", "/p:Platform=x64"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile", "dependsOn": ["Build ImageMatterShared (Release)"]}, {"label": "Post-Build Events (Debug)", "type": "shell", "command": "cmd", "args": ["/c", "if not exist \"x64\\Debug\\ModelsVggHeadDetector\" mkdir \"x64\\Debug\\ModelsVggHeadDetector\" && if not exist \"x64\\Debug\\ModelsMatting\" mkdir \"x64\\Debug\\ModelsMatting\" && (xcopy /E /I /Y /D \"$(VccLibs)\\onnxruntime\\lib\\*.dll\" \"x64\\Debug\" 2>nul || exit /b 0) && (xcopy /E /I /Y /D \"$(VccLibs)\\ffmpeg\\bin\\*.dll\" \"x64\\Debug\" 2>nul || exit /b 0) && (xcopy /E /I /Y /D \"$(VccLibs)\\TensorRT\\lib\\*.dll\" \"x64\\Debug\" 2>nul || exit /b 0) && (xcopy /E /I /Y /D \"$(VccLibs)\\TensorRT\\bin\\*.exe\" \"x64\\Debug\" 2>nul || exit /b 0) && (xcopy /E /I /Y /D \"ModelsVggHeadDetector\\*.onnx\" \"x64\\Debug\\ModelsVggHeadDetector\" 2>nul || exit /b 0) && (xcopy /E /I /Y /D \"ModelsMatting\\*.*\" \"x64\\Debug\\ModelsMatting\" 2>nul || exit /b 0)"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "runOptions": {"runOn": "folderOpen"}}, {"label": "Post-Build Events (Release)", "type": "shell", "command": "cmd", "args": ["/c", "if not exist \"x64\\Release\\ModelsVggHeadDetector\" mkdir \"x64\\Release\\ModelsVggHeadDetector\" && if not exist \"x64\\Release\\ModelsMatting\" mkdir \"x64\\Release\\ModelsMatting\" && (xcopy /E /I /Y /D \"$(VccLibs)\\onnxruntime\\lib\\*.dll\" \"x64\\Release\" 2>nul || exit /b 0) && (xcopy /E /I /Y /D \"$(VccLibs)\\ffmpeg\\bin\\*.dll\" \"x64\\Release\" 2>nul || exit /b 0) && (xcopy /E /I /Y /D \"$(VccLibs)\\TensorRT\\lib\\*.dll\" \"x64\\Release\" 2>nul || exit /b 0) && (xcopy /E /I /Y /D \"$(VccLibs)\\TensorRT\\bin\\*.exe\" \"x64\\Release\" 2>nul || exit /b 0) && (xcopy /E /I /Y /D \"ModelsVggHeadDetector\\*.onnx\" \"x64\\Release\\ModelsVggHeadDetector\" 2>nul || exit /b 0) && (xcopy /E /I /Y /D \"ModelsMatting\\*.*\" \"x64\\Release\\ModelsMatting\" 2>nul || exit /b 0)"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "runOptions": {"runOn": "folderOpen"}}, {"label": "Run (Debug)", "type": "shell", "command": "x64/Debug/ImageMatterApp.exe", "args": [], "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared"}, "dependsOn": ["Build ImageMatterApp (Debug)", "Post-Build Events (Debug)"], "dependsOrder": "sequence"}, {"label": "Run (Release)", "type": "shell", "command": "x64/Release/ImageMatterApp.exe", "args": [], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared"}, "dependsOn": ["Build ImageMatterApp (Release)", "Post-Build Events (Release)"], "dependsOrder": "sequence"}, {"label": "Debug (Launch)", "type": "shell", "command": "devenv", "args": ["ImageMatter.sln", "/debugexe", "x64/Debug/ImageMatterApp.exe"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared"}, "dependsOn": ["Build ImageMatterApp (Debug)", "Post-Build Events (Debug)"], "dependsOrder": "sequence"}, {"label": "Clean All", "type": "shell", "command": "msbuild", "args": ["ImageMatter.sln", "/t:Clean", "/p:Configuration=Debug;Release", "/p:Platform=x64"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "Clean Debug", "type": "shell", "command": "msbuild", "args": ["ImageMatter.sln", "/t:Clean", "/p:Configuration=Debug", "/p:Platform=x64"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "Clean Release", "type": "shell", "command": "msbuild", "args": ["ImageMatter.sln", "/t:Clean", "/p:Configuration=Release", "/p:Platform=x64"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile"}, {"label": "Rebuild All (Debug)", "type": "shell", "command": "msbuild", "args": ["ImageMatter.sln", "/t:Rebuild", "/p:Configuration=Debug", "/p:Platform=x64", "/m"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile", "dependsOrder": "sequence", "dependsOn": ["Post-Build Events (Debug)"]}, {"label": "Rebuild All (Release)", "type": "shell", "command": "msbuild", "args": ["ImageMatter.sln", "/t:Rebuild", "/p:Configuration=Release", "/p:Platform=x64", "/m"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile", "dependsOrder": "sequence", "dependsOn": ["Post-Build Events (Release)"]}, {"label": "Build with CUDA (Debug)", "type": "shell", "command": "msbuild", "args": ["ImageMatter.sln", "/p:Configuration=Debug", "/p:Platform=x64", "/p:CudaCompilerAdditionalOptions=--expt-relaxed-constexpr", "/m"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile", "dependsOrder": "sequence", "dependsOn": ["Post-Build Events (Debug)"]}, {"label": "Build with CUDA (Release)", "type": "shell", "command": "msbuild", "args": ["ImageMatter.sln", "/p:Configuration=Release", "/p:Platform=x64", "/p:CudaCompilerAdditionalOptions=--expt-relaxed-constexpr", "/m"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$msCompile", "dependsOrder": "sequence", "dependsOn": ["Post-Build Events (Release)"]}]}