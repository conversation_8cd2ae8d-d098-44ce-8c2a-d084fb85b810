﻿#include "DownscaleResizingKernels.cuh"
#include <device_launch_parameters.h>
#include <Windows.h>
#include <iostream>
#include <math_constants.h>

#ifdef __CUDACC__


// CUDA kernel for Lanczos-3 resizing that preserves fine details like hair
__device__ inline float lanczos3(float x) {
    if (x == 0.0f) return 1.0f;
    if (fabsf(x) >= 3.0f) return 0.0f;

    float pi_x = 3.14159265359f * x;
    float pi_x_div3 = pi_x / 3.0f;
    return 3.0f * sinf(pi_x) * sinf(pi_x_div3) / (pi_x * pi_x);
}

__global__ void LanczosResizeDownKernel(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels) {
    const int x = blockIdx.x * blockDim.x + threadIdx.x;
    const int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x < outputWidth && y < outputHeight) {
        // Calculate source coordinates
        float scaleX = static_cast<float>(inputWidth) / outputWidth;
        float scaleY = static_cast<float>(inputHeight) / outputHeight;
        float srcX = (static_cast<float>(x) + 0.5f) * scaleX - 0.5f;
        float srcY = (static_cast<float>(y) + 0.5f) * scaleY - 0.5f;

        // Lanczos-3 support is 3 pixels in each direction
        int x_start = max(0, static_cast<int>(floorf(srcX)) - 2);
        int x_end = min(inputWidth - 1, static_cast<int>(ceilf(srcX)) + 2);
        int y_start = max(0, static_cast<int>(floorf(srcY)) - 2);
        int y_end = min(inputHeight - 1, static_cast<int>(ceilf(srcY)) + 2);

        for (int c = 0; c < channels; c++) {
            float sum = 0.0f;
            float weightSum = 0.0f;

            int planeSize = inputWidth * inputHeight;
            int planeOffset = c * planeSize;

            // Apply Lanczos-3 filter
            for (int iy = y_start; iy <= y_end; iy++) {
                for (int ix = x_start; ix <= x_end; ix++) {
                    float dx = srcX - static_cast<float>(ix);
                    float dy = srcY - static_cast<float>(iy);

                    float weightX = lanczos3(dx);
                    float weightY = lanczos3(dy);
                    float weight = weightX * weightY;

                    if (weight != 0.0f) {
                        float pixel = input[planeOffset + (iy * inputWidth + ix)];
                        sum += pixel * weight;
                        weightSum += weight;
                    }
                }
            }

            // Normalize and store result
            float value = (weightSum > 0.0f) ? sum / weightSum : 0.0f;

            int outPlaneSize = outputWidth * outputHeight;
            int outPlaneOffset = c * outPlaneSize;
            output[outPlaneOffset + (y * outputWidth + x)] = value;
        }
    }
}

cudaError_t LaunchLanczosResizeDownKernel(float* output, int outputWidth, int outputHeight,
    float* input, int inputWidth, int inputHeight, int channels) {
    dim3 blockDim(16, 16);
    dim3 gridDim(
        (outputWidth + blockDim.x - 1) / blockDim.x,
        (outputHeight + blockDim.y - 1) / blockDim.y
    );
    LanczosResizeDownKernel << <gridDim, blockDim >> > (
        output, outputWidth, outputHeight,
        input, inputWidth, inputHeight, channels);
    return cudaGetLastError();
}

// Helper function to resize an image using Lanczos interpolation
cudaError_t LanczosResizeKernelLauncher(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels, cudaStream_t stream) {
    dim3 blockDim(16, 16);
    dim3 gridDim((outputWidth + blockDim.x - 1) / blockDim.x, (outputHeight + blockDim.y - 1) / blockDim.y);

    LanczosResizeDownKernel << <gridDim, blockDim, 0, stream >> > (output, outputWidth, outputHeight,
        input, inputWidth, inputHeight, channels);

    return cudaGetLastError();
}

// Alternative: Mitchell-Netravali filter for even better detail preservation
__device__ inline float mitchell(float x) {
    x = fabsf(x);
    if (x >= 2.0f) return 0.0f;

    const float B = 1.0f / 3.0f;
    const float C = 1.0f / 3.0f;

    if (x < 1.0f) {
        return ((12.0f - 9.0f * B - 6.0f * C) * x * x * x +
            (-18.0f + 12.0f * B + 6.0f * C) * x * x +
            (6.0f - 2.0f * B)) / 6.0f;
    }
    else {
        return ((-B - 6.0f * C) * x * x * x +
            (6.0f * B + 30.0f * C) * x * x +
            (-12.0f * B - 48.0f * C) * x +
            (8.0f * B + 24.0f * C)) / 6.0f;
    }
}

__global__ void MitchellResizeDownKernel(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels) {
    const int x = blockIdx.x * blockDim.x + threadIdx.x;
    const int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x < outputWidth && y < outputHeight) {
        float scaleX = static_cast<float>(inputWidth) / outputWidth;
        float scaleY = static_cast<float>(inputHeight) / outputHeight;
        float srcX = (static_cast<float>(x) + 0.5f) * scaleX - 0.5f;
        float srcY = (static_cast<float>(y) + 0.5f) * scaleY - 0.5f;

        // Mitchell filter has support of 2 pixels in each direction
        int x_start = max(0, static_cast<int>(floorf(srcX)) - 1);
        int x_end = min(inputWidth - 1, static_cast<int>(ceilf(srcX)) + 1);
        int y_start = max(0, static_cast<int>(floorf(srcY)) - 1);
        int y_end = min(inputHeight - 1, static_cast<int>(ceilf(srcY)) + 1);

        for (int c = 0; c < channels; c++) {
            float sum = 0.0f;
            float weightSum = 0.0f;

            int planeSize = inputWidth * inputHeight;
            int planeOffset = c * planeSize;

            for (int iy = y_start; iy <= y_end; iy++) {
                for (int ix = x_start; ix <= x_end; ix++) {
                    float dx = srcX - static_cast<float>(ix);
                    float dy = srcY - static_cast<float>(iy);

                    float weightX = mitchell(dx);
                    float weightY = mitchell(dy);
                    float weight = weightX * weightY;

                    if (weight != 0.0f) {
                        float pixel = input[planeOffset + (iy * inputWidth + ix)];
                        sum += pixel * weight;
                        weightSum += weight;
                    }
                }
            }

            float value = (weightSum > 0.0f) ? sum / weightSum : 0.0f;

            int outPlaneSize = outputWidth * outputHeight;
            int outPlaneOffset = c * outPlaneSize;
            output[outPlaneOffset + (y * outputWidth + x)] = value;
        }
    }
}

cudaError_t LaunchMitchellResizeDownKernel(float* output, int outputWidth, int outputHeight,
    float* input, int inputWidth, int inputHeight, int channels) {
    dim3 blockDim(16, 16);
    dim3 gridDim(
        (outputWidth + blockDim.x - 1) / blockDim.x,
        (outputHeight + blockDim.y - 1) / blockDim.y
    );
    MitchellResizeDownKernel << <gridDim, blockDim >> > (
        output, outputWidth, outputHeight,
        input, inputWidth, inputHeight, channels);
    return cudaGetLastError();
}

// Helper function to resize an image using Mitchell interpolation
cudaError_t MitchellResizeKernelLauncher(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels, cudaStream_t stream) {
    dim3 blockDim(16, 16);
    dim3 gridDim((outputWidth + blockDim.x - 1) / blockDim.x, (outputHeight + blockDim.y - 1) / blockDim.y);

    MitchellResizeDownKernel << <gridDim, blockDim, 0, stream >> > (output, outputWidth, outputHeight,
        input, inputWidth, inputHeight, channels);

    return cudaGetLastError();
}
#endif