Identity=src\UpscaleResizingKernels.cu
AdditionalCompilerOptions=
AdditionalCompilerOptions=
AdditionalDependencies=
AdditionalDeps=
AdditionalLibraryDirectories=
AdditionalOptions=--expt-relaxed-constexpr
AdditionalOptions=--expt-relaxed-constexpr
CodeGeneration=compute_52,sm_52
CodeGeneration=compute_52,sm_52
CompileOut=F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterApp\x64\Debug\UpscaleResizingKernels.cu.obj
CudaRuntime=Static
CudaToolkitCustomDir=
DebugInformationFormat=ProgramDatabase
DebugInformationFormat=ProgramDatabase
Defines=;WIN32;WIN64;_DEBUG;_CONSOLE;_MBCS;
Emulation=false
EnableVirtualArchInFatbin=true
ExtensibleWholeProgramCompilation=false
FastCompile=Off
FastMath=false
GenerateLineInfo=false
GenerateRelocatableDeviceCode=false
GPUDebugInfo=true
GPUDebugInfo=true
HostDebugInfo=true
Include=;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include;F:\VccLibs\ffmpeg\include;F:\VccLibs\onnxruntime\include;include;F:\VccLibs\TensorRT\include;;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include
Inputs=
InterleaveSourceInPTX=false
Keep=false
KeepDir=x64\Debug
LinkOut=
MaxRegCount=0
NvccCompilation=compile
NvccPath=
Optimization=Od
Optimization=Od
PerformDeviceLink=
PerformDeviceLinkTimeOptimization=
PtxAsOptionV=false
RequiredIncludes=
Runtime=MDd
Runtime=MDd
RuntimeChecks=RTC1
RuntimeChecks=RTC1
SplitCompile=Default
SplitCompileCustomThreads=
TargetMachinePlatform=64
TargetMachinePlatform=64
TypeInfo=
TypeInfo=
UseHostDefines=true
UseHostInclude=true
UseHostLibraryDependencies=
UseHostLibraryDirectories=
Warning=W3
Warning=W3
