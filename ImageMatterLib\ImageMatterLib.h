#pragma once

#ifdef IMAGEMATTERLIB_EXPORTS
#define IMAGEMATTERLIB_API __declspec(dllexport)
#else
#define IMAGEMATTERLIB_API __declspec(dllimport)
#endif

extern "C" {

// Engine type enum (matches C#)
enum EngineType {
    ENGINE_ONNX = 0,
    ENGINE_TENSORRT = 1,
    ENGINE_AUTO = 2
};

// Progress callback: returns true to continue, false to cancel
// currentFrame: 0-based index, totalFrames: total number of frames (if known, else 0)
typedef bool (__stdcall *ProgressCallback)(int currentFrame, int totalFrames, void* userData);

// Main function: returns 0 on success, nonzero on error/cancel
IMAGEMATTERLIB_API int ConvertVideoToProRes4444(
    const wchar_t* inputVideo,
    const wchar_t* outputVideo,
    EngineType engine,
    ProgressCallback progressCb,
    void* userData
);

// DirectVideoReaderAlpha C interface
// Handle type for DirectVideoReaderAlpha instances
typedef void* VideoReaderAlphaHandle;

// Create a DirectVideoReaderAlpha instance
// Returns handle on success, nullptr on failure
IMAGEMATTERLIB_API VideoReaderAlphaHandle CreateVideoReaderAlpha(const wchar_t* filePath);

// Destroy a DirectVideoReaderAlpha instance
IMAGEMATTERLIB_API void DestroyVideoReaderAlpha(VideoReaderAlphaHandle handle);

// Get video properties
IMAGEMATTERLIB_API int GetVideoWidth(VideoReaderAlphaHandle handle);
IMAGEMATTERLIB_API int GetVideoHeight(VideoReaderAlphaHandle handle);
IMAGEMATTERLIB_API double GetVideoDuration(VideoReaderAlphaHandle handle);
IMAGEMATTERLIB_API double GetVideoFrameRate(VideoReaderAlphaHandle handle);
IMAGEMATTERLIB_API bool GetVideoHasAlpha(VideoReaderAlphaHandle handle);
IMAGEMATTERLIB_API int GetVideoRgbaBufferSize(VideoReaderAlphaHandle handle);

// Get pixel format name (copies to provided buffer, returns length needed)
// Returns actual string length (excluding null terminator)
// If buffer is too small, only bufferSize-1 chars are copied and null terminated
IMAGEMATTERLIB_API int GetVideoPixelFormatName(VideoReaderAlphaHandle handle, char* buffer, int bufferSize);

// Seek to a specific time position (in seconds)
// Returns true on success, false on failure
IMAGEMATTERLIB_API bool SeekVideo(VideoReaderAlphaHandle handle, double timeInSeconds);

// Read the next frame into CPU memory as RGBA
// cpuRgbaBuffer must be at least GetVideoRgbaBufferSize() bytes
// Returns frame timestamp in seconds, or negative value on error/EOF
IMAGEMATTERLIB_API double ReadVideoFrame(VideoReaderAlphaHandle handle, void* cpuRgbaBuffer, int bufferSize);

}
