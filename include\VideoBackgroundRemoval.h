#pragma once

#include <string>

// Progress callback type: returns true to continue, false to cancel
// currentFrame: 0-based index, totalFrames: total number of frames (if known, else 0)
typedef bool (__stdcall *ProgressCallback)(int currentFrame, int totalFrames, void* userData);

// Engine type enum
enum EngineType {
    ENGINE_ONNX = 0,
    ENGINE_TENSORRT = 1,
    ENGINE_AUTO = 2
};

// Main function for video background removal and ProRes4444 export
// Returns 0 on success, nonzero on error/cancel
//
// Parameters:
// - inputVideo: Path to input video file
// - outputVideo: Path to output ProRes4444 video file
// - engine: Engine type to use for AI processing
// - progressCb: Optional callback for progress updates (can be nullptr)
// - userData: Optional user data passed to progress callback
int VideoBackgroundRemoval(
    const std::wstring& inputVideo,
    const std::wstring& outputVideo,
    EngineType engine,
    ProgressCallback progressCb,
    void* userData
);

// Overload with twoPhase parameter for backward compatibility
int VideoBackgroundRemoval(
    const std::wstring& inputVideo,
    const std::wstring& outputVideo,
    EngineType engine,
    bool twoPhase,
    ProgressCallback progressCb,
    void* userData
);
