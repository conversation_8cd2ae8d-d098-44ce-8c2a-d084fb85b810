// DirectVideoReaderAlpha.h
#pragma once

#include <string>
#include <memory>
#include <vector>

#include "Helpers.h"

// Include AVRational definition
extern "C" {
#include <libavutil/rational.h>
}

// Forward declarations of FFmpeg structures
struct AVFormatContext;
struct AVCodecContext;
struct AVFrame;
struct AVPacket;
struct SwsContext;

class DirectVideoReaderAlpha {
public:
    // Initialize and open a video file with alpha channel support (CPU-only)
    static std::shared_ptr<DirectVideoReaderAlpha> Create(const std::string& filePath);

    // Destructor - will handle cleanup. Called automatically by std::shared_ptr.
    ~DirectVideoReaderAlpha();

    // Seek to a specific time position (in seconds)
    bool Seek(double timeInSeconds);

    // Read the next frame into CPU memory as RGBA, returns the frame timestamp
    // The caller must provide a buffer of at least GetWidth() * GetHeight() * 4 bytes
    double ReadFrame(void* cpuRgbaBuffer, size_t bufferSize);

    // Get video properties
    int GetWidth() const { return m_width; }
    int GetHeight() const { return m_height; }
    double GetDuration() const { return m_duration; }
    AVRational GetFrameRate() const { return m_frameRate; }
    double GetFrameRateDouble() const { return m_frameRate.num / static_cast<double>(m_frameRate.den); }
    VideoUtils::TextureFormat GetTextureFormat() const { return VideoUtils::TextureFormat::RGBA; }
    AVRational GetTimeBase() const { return m_timeBase; }
    double GetTimeBaseDouble() const { return m_timeBase.num / static_cast<double>(m_timeBase.den); }

    // Get underlying video stream format (can be called before reading frames)
    VideoUtils::TextureFormat GetVideoStreamFormat() const;

    // Check if the video has an alpha channel
    bool HasAlphaChannel() const { return m_hasAlpha; }

    // Get the pixel format name
    std::string GetPixelFormatName() const { return m_pixelFormatName; }

    // Get the required buffer size for RGBA frames
    size_t GetRgbaBufferSize() const { return m_width * m_height * 4; }

    // Close the video and release resources
    void Close();

private:
    // Private constructor - use Create() to create instances
    DirectVideoReaderAlpha();

    // Initialize FFmpeg resources
    bool Initialize(const std::string& filePath);

    // Decode a single packet
    bool DecodePacket();

    // Convert frame to RGBA in CPU memory
    bool ConvertFrameToRgba(void* cpuRgbaBuffer, size_t bufferSize);

    // Convert frame timestamp to seconds
    double ConvertTimestampToSeconds(int64_t timestamp) const;

    // Check if pixel format has alpha channel
    bool PixelFormatHasAlpha(int pixelFormat) const;

private:
    // FFmpeg state
    AVFormatContext* m_formatContext;
    AVCodecContext* m_codecContext;
    AVFrame* m_frame;
    AVFrame* m_rgbaFrame;  // Frame for RGBA conversion
    AVPacket* m_packet;
    SwsContext* m_swsContext; // For color space conversion
    int m_videoStreamIndex;

    // Video properties
    int m_width;
    int m_height;
    double m_duration;
    AVRational m_frameRate;
    AVRational m_timeBase;
    bool m_hasAlpha;
    std::string m_pixelFormatName;

    // Current frame timestamp
    int64_t m_currentFrameTimestamp;

    // State tracking
    bool m_isInitialized;
    bool m_isEof;
};
