#include "FrameMetadataStorage.h"
#include <iostream>
#include <sstream>
#include <random>

// Constructor
FrameMetadataStorage::FrameMetadataStorage()
    : m_maxFrames(0)
    , m_fileSize(0)
    , m_fileHandle(INVALID_HANDLE_VALUE)
    , m_mappingHandle(nullptr)
    , m_mappedMemory(nullptr)
    , m_initialized(false) {
}

// Destructor
FrameMetadataStorage::~FrameMetadataStorage() {
    Close();
}

// Create factory method
std::unique_ptr<FrameMetadataStorage> FrameMetadataStorage::Create(
    const std::wstring& filePath, 
    int maxFrames, 
    bool createNew) {
    
    auto storage = std::unique_ptr<FrameMetadataStorage>(new FrameMetadataStorage());
    
    if (!storage->Initialize(filePath, maxFrames, createNew)) {
        return nullptr;
    }
    
    return storage;
}

// Initialize the storage
bool FrameMetadataStorage::Initialize(const std::wstring& filePath, int maxFrames, bool createNew) {
    if (maxFrames <= 0) {
        std::wcerr << L"Invalid maxFrames: " << maxFrames << std::endl;
        return false;
    }
    
    m_filePath = filePath;
    m_maxFrames = maxFrames;
    m_fileSize = sizeof(FrameMetadata) * maxFrames;
    
    // Create or open the file
    DWORD creationDisposition = createNew ? CREATE_ALWAYS : OPEN_EXISTING;
    m_fileHandle = CreateFileW(
        filePath.c_str(),
        GENERIC_READ | GENERIC_WRITE,
        0, // No sharing
        nullptr,
        creationDisposition,
        FILE_ATTRIBUTE_NORMAL,
        nullptr
    );
    
    if (m_fileHandle == INVALID_HANDLE_VALUE) {
        std::wcerr << L"Failed to create/open file: " << filePath << L", Error: " << GetLastError() << std::endl;
        return false;
    }
    
    // Set file size if creating new
    if (createNew) {
        LARGE_INTEGER fileSize;
        fileSize.QuadPart = m_fileSize;
        if (!SetFilePointerEx(m_fileHandle, fileSize, nullptr, FILE_BEGIN) ||
            !SetEndOfFile(m_fileHandle)) {
            std::wcerr << L"Failed to set file size, Error: " << GetLastError() << std::endl;
            Close();
            return false;
        }
    }
    
    // Create file mapping
    m_mappingHandle = CreateFileMappingW(
        m_fileHandle,
        nullptr,
        PAGE_READWRITE,
        0, 0, // Use entire file
        nullptr
    );
    
    if (!m_mappingHandle) {
        std::wcerr << L"Failed to create file mapping, Error: " << GetLastError() << std::endl;
        Close();
        return false;
    }
    
    // Map the file into memory
    m_mappedMemory = MapViewOfFile(
        m_mappingHandle,
        FILE_MAP_ALL_ACCESS,
        0, 0, // Map entire file
        0
    );
    
    if (!m_mappedMemory) {
        std::wcerr << L"Failed to map view of file, Error: " << GetLastError() << std::endl;
        Close();
        return false;
    }
    
    // Initialize frame metadata if creating new file
    if (createNew) {
        FrameMetadata* frames = static_cast<FrameMetadata*>(m_mappedMemory);
        for (int i = 0; i < maxFrames; ++i) {
            frames[i].Reset();
            frames[i].frameIndex = i;
        }
    }
    
    m_initialized = true;
    return true;
}

// Close and cleanup
void FrameMetadataStorage::Close() {
    if (m_mappedMemory) {
        UnmapViewOfFile(m_mappedMemory);
        m_mappedMemory = nullptr;
    }
    
    if (m_mappingHandle) {
        CloseHandle(m_mappingHandle);
        m_mappingHandle = nullptr;
    }
    
    if (m_fileHandle != INVALID_HANDLE_VALUE) {
        CloseHandle(m_fileHandle);
        m_fileHandle = INVALID_HANDLE_VALUE;
    }
    
    m_initialized = false;
}

// Get frame metadata by index
FrameMetadata* FrameMetadataStorage::GetFrameMetadata(int frameIndex) {
    if (!m_initialized || frameIndex < 0 || frameIndex >= m_maxFrames) {
        return nullptr;
    }
    
    FrameMetadata* frames = static_cast<FrameMetadata*>(m_mappedMemory);
    return &frames[frameIndex];
}

// Get frame metadata by index (const version)
const FrameMetadata* FrameMetadataStorage::GetFrameMetadata(int frameIndex) const {
    if (!m_initialized || frameIndex < 0 || frameIndex >= m_maxFrames) {
        return nullptr;
    }
    
    const FrameMetadata* frames = static_cast<const FrameMetadata*>(m_mappedMemory);
    return &frames[frameIndex];
}

// Set frame metadata by index
bool FrameMetadataStorage::SetFrameMetadata(int frameIndex, const FrameMetadata& metadata) {
    FrameMetadata* frame = GetFrameMetadata(frameIndex);
    if (!frame) {
        return false;
    }
    
    *frame = metadata;
    frame->frameIndex = frameIndex; // Ensure frame index is correct
    return true;
}

// Flush any pending writes to disk
bool FrameMetadataStorage::Flush() {
    if (!m_initialized) {
        return false;
    }
    
    return FlushViewOfFile(m_mappedMemory, 0) != 0;
}

// TemporaryFrameMetadataStorage implementation

TemporaryFrameMetadataStorage::TemporaryFrameMetadataStorage(int maxFrames, const std::wstring& tempDir)
    : m_deleteOnDestroy(true) {
    
    // Generate temporary file path
    std::wstring baseTempDir = tempDir;
    if (baseTempDir.empty()) {
        wchar_t tempPath[MAX_PATH];
        if (GetTempPathW(MAX_PATH, tempPath) > 0) {
            baseTempDir = tempPath;
        } else {
            baseTempDir = L".\\"; // Fallback to current directory
        }
    }
    
    // Generate unique filename
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(1000, 9999);
    
    std::wstringstream ss;
    ss << baseTempDir;
    if (baseTempDir.back() != L'\\' && baseTempDir.back() != L'/') {
        ss << L"\\";
    }
    ss << L"frame_metadata_" << GetCurrentProcessId() << L"_" << dis(gen) << L".tmp";
    m_tempFilePath = ss.str();
    
    // Create the storage
    m_storage = FrameMetadataStorage::Create(m_tempFilePath, maxFrames, true);
    if (!m_storage) {
        std::wcerr << L"Failed to create temporary frame metadata storage: " << m_tempFilePath << std::endl;
    }
}

TemporaryFrameMetadataStorage::~TemporaryFrameMetadataStorage() {
    // Close storage first
    m_storage.reset();
    
    // Delete temporary file
    if (m_deleteOnDestroy && !m_tempFilePath.empty()) {
        DeleteFileW(m_tempFilePath.c_str());
    }
}
