﻿  alpha_codec.cpp
  AsyncDirectVideoReader.cpp
  CudaProResEncoder.cpp
  DetectHeadsStep.cpp
  DirectVideoReader.cpp
  DirectVideoReaderAlpha.cpp
  DirectVideoWriter.cpp
  DirectVideoWriterAlphaCuda.cpp
  FinalInitialAlphaStep.cpp
  FrameMetadataStorage.cpp
  FrameProcessor.cpp
  HeadDetector.cpp
  Helpers.cpp
  Helpers_Heads.cpp
  ImageMatting.cpp
  ImageMattingFactory.cpp
  ImageMattingOnnx.cpp
  ImageMattingTensorRt.cpp
  lodepng.cpp
  ProcessBodyRegionsStep.cpp
  Génération de code en cours...
  Compilation en cours...
  ProcessHeadRegionsStep.cpp
  ProcessingStep.cpp
  VideoBackgroundRemoval.cpp
F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterShared\src\VideoBackgroundRemoval.cpp(532,34): warning C4244: 'initialisation' : conversion de 'const _Ty' en 'int', perte possible de données
F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterShared\src\VideoBackgroundRemoval.cpp(532,34): warning C4244:         with
F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterShared\src\VideoBackgroundRemoval.cpp(532,34): warning C4244:         [
F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterShared\src\VideoBackgroundRemoval.cpp(532,34): warning C4244:             _Ty=float
F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterShared\src\VideoBackgroundRemoval.cpp(532,34): warning C4244:         ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: '=' : conversion de 'const wchar_t' en 'char', perte possible de données
  (compiler le fichier source 'src/VideoBackgroundRemoval.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      le contexte d’instanciation du modèle (le plus ancien) est
          F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterShared\src\VideoBackgroundRemoval.cpp(400,33):
          voir la référence à l'instanciation de la fonction modèle 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_const_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' en cours de compilation
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterShared\src\VideoBackgroundRemoval.cpp(400,33):
              voir la première référence à « std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string » dans « ProcessMultiPass »
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          voir la référence à l'instanciation de la fonction modèle 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<const wchar_t*,const wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' en cours de compilation
          with
          [
              _Size_type=unsigned __int64,
              _Iter=const wchar_t *,
              _Sent=const wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          voir la référence à l'instanciation de la fonction modèle '_OutIt *std::_Copy_n_unchecked4<const wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' en cours de compilation
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=const wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
  VideoWriterAlpha.cpp
  Génération de code en cours...
  ImageMatterShared.vcxproj -> F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Debug\ImageMatterShared.lib
  0 fichier(s) copiÚ(s)
  0 fichier(s) copiÚ(s)
  0 fichier(s) copiÚ(s)
  0 fichier(s) copiÚ(s)
  0 fichier(s) copiÚ(s)
  Fichier introuvable - *.onnx
