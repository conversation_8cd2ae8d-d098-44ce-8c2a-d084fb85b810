// DirectVideoWriterAlphaCuda.cpp
#include "DirectVideoWriterAlphaCuda.h"
#include "CudaProResEncoder.h"
#include <cuda.h>  // CUDA Driver API
#include <cuda_runtime.h>
#include "Helpers.h"
#include <iostream>
#include <chrono>

// Constructor
DirectVideoWriterAlphaCuda::DirectVideoWriterAlphaCuda()
    : m_cudaContext(nullptr)
    , m_pts(0)
    , m_isInitialized(false)
    , m_isFinalized(false)
    , m_encodingStream(nullptr)
{
}

// Destructor
DirectVideoWriterAlphaCuda::~DirectVideoWriterAlphaCuda() {
    // Stop encoding thread if still running
    if (!m_stopEncoding.load()) {
        m_stopEncoding = true;
        m_queueCondition.notify_all();
        m_spaceAvailableCondition.notify_all();
        if (m_encodingThread.joinable()) {
            m_encodingThread.join();
        }
    }

    // Clean up CUDA stream
    if (m_encodingStream) {
        cudaStreamDestroy(m_encodingStream);
    }
}

// Factory method
std::unique_ptr<DirectVideoWriterAlphaCuda> DirectVideoWriterAlphaCuda::Create(const OutputConfig& config, CUcontext cudaContext) {
    std::unique_ptr<DirectVideoWriterAlphaCuda> writer(new DirectVideoWriterAlphaCuda());
    if (writer->Initialize(config, cudaContext)) {
        return writer;
    }
    return nullptr;
}

// Initialize the writer
bool DirectVideoWriterAlphaCuda::Initialize(const OutputConfig& config, CUcontext cudaContext) {
    try {
        m_config = config;
        m_cudaContext = cudaContext;

        // Create CUDA ProRes encoder
        CudaProResEncoder::Config encoderConfig;
        encoderConfig.width = config.width;
        encoderConfig.height = config.height;
        encoderConfig.frameRate = config.frameRate;
        encoderConfig.quality = config.quality;
        encoderConfig.outputPath = config.outputPath;
        encoderConfig.enableAlpha = config.enableAlpha;

        if (config.quality == 3) {
            encoderConfig.SetProRes4444();
        } else if (config.quality == 4) {
            encoderConfig.SetProRes4444XQ();
        }

        m_encoder = CudaProResEncoder::Create(encoderConfig, cudaContext);
        if (!m_encoder) {
            std::cerr << "Failed to create CUDA ProRes encoder" << std::endl;
            return false;
        }

        // Create dedicated CUDA stream for encoding operations
        CUDA_CHECK(cudaStreamCreate(&m_encodingStream));

        // Start asynchronous encoding thread
        m_encodingThread = std::thread(&DirectVideoWriterAlphaCuda::EncodingThreadFunction, this);

        m_isInitialized = true;
        std::cout << "DirectVideoWriterAlphaCuda: Initialized with CUDA ProRes 4444 encoding" << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error initializing DirectVideoWriterAlphaCuda: " << e.what() << std::endl;
        return false;
    }
}

// Asynchronous encoding thread function
void DirectVideoWriterAlphaCuda::EncodingThreadFunction() {
    // Set CUDA context for this thread
    CUresult contextResult = cuCtxSetCurrent(m_cudaContext);
    if (contextResult != CUDA_SUCCESS) {
        std::cerr << "ERROR: Failed to set CUDA context in encoding thread: " << contextResult << std::endl;
        m_encodingError = true;
        return;
    }

    std::cout << "DirectVideoWriterAlphaCuda: Encoding thread started" << std::endl;

    while (true) {
        std::unique_lock<std::mutex> lock(m_queueMutex);

        m_queueCondition.wait(lock, [this] {
            return !m_frameQueue.empty() || m_stopEncoding.load();
        });

        if (m_frameQueue.empty() && m_stopEncoding.load()) {
            break;
        }

        if (!m_frameQueue.empty()) {
            std::unique_ptr<FrameData> frameData = std::move(m_frameQueue.front());
            m_frameQueue.pop();

            // Notify that space is available in queue
            m_spaceAvailableCondition.notify_one();

            lock.unlock();

            if (frameData->isLastFrame) {
                break;
            }

            try {
                // Wait for frame copy to complete before encoding
                frameData->WaitForCopyComplete();

                // Encode frame using CUDA ProRes encoder
                if (!m_encoder->WriteFrame(frameData->cudaRgbaBuffer, frameData->bufferSize)) {
                    std::cerr << "DirectVideoWriterAlphaCuda: Failed to encode frame" << std::endl;
                    m_encodingError = true;
                    break;
                }
            }
            catch (const std::exception& e) {
                std::cerr << "DirectVideoWriterAlphaCuda: Error in encoding thread: " << e.what() << std::endl;
                m_encodingError = true;
                break;
            }
        }
    }

    std::cout << "DirectVideoWriterAlphaCuda: Encoding thread finished" << std::endl;
}

// Write a frame asynchronously
bool DirectVideoWriterAlphaCuda::WriteFrame(void* cudaRgbaBuffer, size_t bufferSize) {
    if (!m_isInitialized || !cudaRgbaBuffer || m_isFinalized) return false;

    // Check for encoding errors first
    if (m_encodingError.load()) {
        std::cerr << "DirectVideoWriterAlphaCuda: Encoding error detected, stopping frame submission" << std::endl;
        return false;
    }

    // Efficient blocking approach: wait for space in queue using condition variable
    std::unique_lock<std::mutex> lock(m_queueMutex);

    // Wait for space in queue or error condition
    m_spaceAvailableCondition.wait(lock, [this] {
        return m_frameQueue.size() < MAX_QUEUE_SIZE || m_encodingError.load();
    });

    // Check for encoding errors
    if (m_encodingError.load()) {
        std::cerr << "DirectVideoWriterAlphaCuda: Encoding error detected" << std::endl;
        return false;
    }

    // Calculate PTS for this frame
    double fps = static_cast<double>(m_config.frameRate.num) / m_config.frameRate.den;
    int64_t currentPts = m_pts;
    m_pts += 1;

    // Add frame to encoding queue with asynchronous copy
    m_frameQueue.push(std::make_unique<FrameData>(cudaRgbaBuffer, bufferSize, currentPts, m_encodingStream));

    // Notify encoding thread
    m_queueCondition.notify_one();

    return true;
}

// Get current queue size
size_t DirectVideoWriterAlphaCuda::GetQueueSize() const {
    std::lock_guard<std::mutex> lock(m_queueMutex);
    return m_frameQueue.size();
}

// Get frame count
size_t DirectVideoWriterAlphaCuda::GetFrameCount() const {
    if (m_encoder) {
        return m_encoder->GetFrameCount();
    }
    return 0;
}

// Get total bytes
size_t DirectVideoWriterAlphaCuda::GetTotalBytes() const {
    if (m_encoder) {
        return m_encoder->GetTotalBytes();
    }
    return 0;
}

// Finalize encoding
bool DirectVideoWriterAlphaCuda::Finalize() {
    if (!m_isInitialized || m_isFinalized) return false;

    std::cout << "DirectVideoWriterAlphaCuda: Finalizing - waiting for encoding thread to complete..." << std::endl;

    // Signal encoding thread to stop and wait for it to finish
    m_stopEncoding = true;
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        m_frameQueue.push(std::make_unique<FrameData>(nullptr, 0, 0, m_encodingStream, true)); // Last frame marker
    }
    m_queueCondition.notify_one();

    if (m_encodingThread.joinable()) {
        m_encodingThread.join();
    }

    if (m_encodingError.load()) {
        std::cerr << "DirectVideoWriterAlphaCuda: Encoding errors occurred during processing" << std::endl;
        return false;
    }

    // Finalize the encoder
    if (m_encoder && !m_encoder->Finalize()) {
        std::cerr << "DirectVideoWriterAlphaCuda: Failed to finalize encoder" << std::endl;
        return false;
    }

    m_isFinalized = true;
    std::cout << "DirectVideoWriterAlphaCuda: Finalization complete" << std::endl;
    std::cout << "Final stats: " << GetFrameCount() << " frames, " << GetTotalBytes() << " bytes" << std::endl;
    return true;
}
