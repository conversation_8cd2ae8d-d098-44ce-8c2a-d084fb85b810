#pragma once

#include <cuda_runtime.h>
#include <cuda_texture_types.h>
#include "HeadDetector.h"

/**
 * Structure to hold normalization parameters for preprocessing
 *
 * Example usage:
 *   // Use default InsPyReNet parameters
 *   NormalizationParams defaultParams;
 *
 *   // Use custom parameters
 *   NormalizationParams customParams(0.5f, 0.5f, 0.5f, 0.25f, 0.25f, 0.25f);
 *
 *   // Initialize ImageMatting with custom parameters
 *   imageMatting->Init(modelPath, modelW, modelH, imgW, imgH, customParams, false, stream);
 */
struct NormalizationParams {
    float meanR, meanG, meanB;
    float stdR, stdG, stdB;

    // Default constructor (uses ImageNet parameters)
    NormalizationParams()
        : meanR(0.485f), meanG(0.456f), meanB(0.406f)
        , stdR(0.229f), stdG(0.224f), stdB(0.225f) {
    }

    // Constructor with custom parameters
    NormalizationParams(float mr, float mg, float mb, float sr, float sg, float sb)
        : meanR(mr), meanG(mg), meanB(mb)
        , stdR(sr), stdG(sg), stdB(sb) {
    }

    // Static factory method for ImageNet parameters (explicit)
    static NormalizationParams ImageNet() {
        return NormalizationParams(0.485f, 0.456f, 0.406f, 0.229f, 0.224f, 0.225f);
    }

    // Static factory method for standard normalization (0-1 range)
    static NormalizationParams Standard() {
        return NormalizationParams(0.0f, 0.0f, 0.0f, 1.0f, 1.0f, 1.0f);
    }

    // Static factory method for normalized range (-1 to 1)
    static NormalizationParams Normalized() {
        return NormalizationParams(0.5f, 0.5f, 0.5f, 0.5f, 0.5f, 0.5f);
    }

    // For input range [0, 255] -> output range [-0.5, 0.5]
	static NormalizationParams MinusHalfToPlusHalf() {
		return NormalizationParams(0.5f, 0.5f, 0.5f, 1.0f, 1.0f, 1.0f);
	}
};

/**
 * CUDA kernel for preprocessing an RGB/RGBA buffer with normalization
 * @param outputBuffer Output buffer (normalized RGB values)
 * @param inputBuffer Input buffer (RGB or RGBA format)
 * @param width Width of the image
 * @param height Height of the image
 * @param isRGBA Whether the input buffer is in RGBA format (true) or RGB format (false)
 * @param normParams Normalization parameters (mean and std for RGB channels)
 */
__global__ void PreprocessBufferKernel(float* outputBuffer, const float* inputBuffer, int width, int height, bool isRGBA, NormalizationParams normParams);

/**
 * Launcher function for the PreprocessBufferKernel
 * @param outputBuffer Output buffer (normalized RGB values)
 * @param inputBuffer Input buffer (RGB or RGBA format)
 * @param width Width of the image
 * @param height Height of the image
 * @param isRGBA Whether the input buffer is in RGBA format (true) or RGB format (false)
 * @param normParams Normalization parameters (mean and std for RGB channels)
 * @param stream CUDA stream to use
 * @return CUDA error code
 */
cudaError_t LaunchPreprocessBufferKernel(float* outputBuffer, const float* inputBuffer, int width, int height, bool isRGBA, const NormalizationParams& normParams, cudaStream_t stream);
