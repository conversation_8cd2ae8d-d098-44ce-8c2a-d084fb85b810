// CudaProResEncoder.h
#pragma once

#include <string>
#include <memory>
#include <vector>
#include <cuda.h>
#include <cuda_runtime.h>

extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/rational.h>
#include <libavutil/opt.h>
#include <libavutil/imgutils.h>
}

class CudaProResEncoder {
public:
    // ProRes 4444 configuration
    struct Config {
        int width = 1920;
        int height = 1080;
        AVRational frameRate = {30, 1};
        int quality = 3;  // 0=Proxy, 1=LT, 2=422, 3=4444, 4=4444XQ
        std::string outputPath;
        
        // ProRes 4444 specific settings
        bool enableAlpha = true;
        int bitDepth = 10;  // 10-bit for ProRes 4444
        
        void SetProRes4444() {
            quality = 3;
            enableAlpha = true;
            bitDepth = 10;
        }
        
        void SetProRes4444XQ() {
            quality = 4;
            enableAlpha = true;
            bitDepth = 10;
        }
    };

    // Factory method
    static std::unique_ptr<CudaProResEncoder> Create(const Config& config, CUcontext cudaContext);

    // Destructor
    ~CudaProResEncoder();

    // Write a frame from CUDA RGBA buffer
    bool WriteFrame(void* cudaRgbaBuffer, size_t bufferSize);

    // Finalize encoding and write file
    bool Finalize();

    // Get encoding statistics
    size_t GetFrameCount() const { return m_frameCount; }
    size_t GetTotalBytes() const { return m_totalBytes; }

private:
    // Private constructor
    CudaProResEncoder();

    // Initialize encoder
    bool Initialize(const Config& config, CUcontext cudaContext);

    // ProRes specific methods
    void InitializeQuantizationTables();
    void InitializeHuffmanTables();
    bool InitializeFFmpegMuxer();
    bool WriteFrameToFFmpeg(void* yuvaData, size_t dataSize);
    bool WriteFrameData(); // Legacy method for compatibility

    // Member variables
    Config m_config;
    CUcontext m_cudaContext;
    cudaStream_t m_cudaStream;

    // CUDA buffers
    void* m_cudaYuvaBuffer;      // YUVA 4:4:4:4 10-bit
    void* m_cudaDctBuffer;       // DCT coefficients
    void* m_cudaQuantBuffer;     // Quantized coefficients
    void* m_cudaCompressedBuffer; // Compressed frame data

    // Buffer sizes
    size_t m_yuvaBufferSize;
    size_t m_dctBufferSize;
    size_t m_quantBufferSize;
    size_t m_compressedBufferSize;

    // ProRes tables (on GPU)
    void* m_cudaQuantTables;     // Quantization tables
    void* m_cudaHuffmanTables;   // Huffman encoding tables

    // FFmpeg muxing
    AVFormatContext* m_formatContext;
    AVCodecContext* m_codecContext;
    AVStream* m_videoStream;
    AVFrame* m_avFrame;
    AVPacket* m_avPacket;

    // Frame data and inter-frame compression
    std::vector<uint8_t> m_frameBuffer;
    std::vector<uint8_t> m_previousFrame;  // For inter-frame compression
    std::vector<uint8_t> m_motionVectors;  // Motion estimation data

    // Statistics
    size_t m_frameCount;
    size_t m_totalBytes;
    int64_t m_pts;

    // State
    bool m_isInitialized;
    bool m_isFinalized;

    // ProRes constants
    static const int SLICE_WIDTH = 16;
    static const int SLICE_HEIGHT = 16;
    static const int DCT_BLOCK_SIZE = 8;
    static const uint32_t PRORES_FOURCC = 0x61703468; // 'ap4h' for ProRes 4444
};

// CUDA kernel declarations (implemented in CudaProResKernels.cu)
extern "C" {
    // Convert RGBA to YUVA 4:4:4:4 10-bit
    void launchRgbaToYuva444_10bit(
        float* rgbaData, 
        uint16_t* yuvaData, 
        int width, 
        int height, 
        cudaStream_t stream
    );

    // Forward DCT transform
    void launchForwardDCT(
        uint16_t* yuvaData,
        int16_t* dctCoeffs,
        int width,
        int height,
        int componentStride,
        cudaStream_t stream
    );

    // Quantization
    void launchQuantization(
        int16_t* dctCoeffs,
        int16_t* quantCoeffs,
        uint16_t* quantTables,
        int width,
        int height,
        int quality,
        cudaStream_t stream
    );

    // Huffman encoding (parallel)
    void launchHuffmanEncode(
        int16_t* quantCoeffs,
        uint8_t* compressedData,
        uint32_t* huffmanTables,
        int width,
        int height,
        size_t* outputSize,
        cudaStream_t stream
    );

    // Motion estimation for inter-frame compression
    void launchMotionEstimation(
        uint16_t* currentFrame,
        uint16_t* previousFrame,
        int16_t* motionVectors,
        int width,
        int height,
        int searchRange,
        cudaStream_t stream
    );

    // Inter-frame prediction
    void launchInterFramePrediction(
        uint16_t* currentFrame,
        uint16_t* previousFrame,
        int16_t* motionVectors,
        int16_t* residual,
        int width,
        int height,
        cudaStream_t stream
    );
}
