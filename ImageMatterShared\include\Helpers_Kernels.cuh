#pragma once

#include <cuda_runtime.h>

// Kernel launcher function declarations
bool LaunchDrawRectangleKernel(float* image, int width, int height,
    int x1, int y1, int x2, int y2,
    float r, float g, float b, int thickness,
    cudaStream_t stream);

bool LaunchDrawNumberKernel(float* image, int width, int height,
    int x, int y, int number,
    float r, float g, float b,
    cudaStream_t stream);