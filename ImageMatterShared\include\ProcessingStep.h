#pragma once

#include <memory>
#include <string>
#include <cuda_runtime.h>
#include <cuda.h>

// Forward declarations
struct FrameMetadata;

/**
 * Abstract base class for modular video processing steps
 * Each step manages its own GPU memory and implements a standardized interface
 * for maximum GPU memory efficiency through single-model utilization
 */
class ProcessingStep {
public:
    ProcessingStep() = default;
    virtual ~ProcessingStep() = default;

    /**
     * Initialize the processing step with video parameters and load model into GPU memory
     * @param width Video frame width
     * @param height Video frame height
     * @param cudaContext CUDA context to use for operations
     * @param stream CUDA stream for operations
     * @return True on success, false on failure
     */
    virtual bool Initialize(int width, int height, CUcontext cudaContext, cudaStream_t stream) = 0;

    /**
     * Process a single frame and update frame metadata
     * @param inputNv12Data Input frame data in NV12 format (GPU memory)
     * @param inputPitch Pitch of input data
     * @param frameMetadata Frame metadata to read from and update
     * @return True on success, false on failure
     */
    virtual bool Process(void* inputNv12Data, size_t inputPitch, FrameMetadata& frameMetadata) = 0;

    /**
     * Get the step name for logging and debugging
     * @return Step name as string
     */
    virtual const char* GetStepName() const = 0;

    /**
     * Check if the step is properly initialized
     * @return True if initialized, false otherwise
     */
    virtual bool IsInitialized() const = 0;

protected:
    // Common member variables for derived classes
    int m_videoWidth = 0;
    int m_videoHeight = 0;
    CUcontext m_cudaContext = nullptr;
    cudaStream_t m_stream = nullptr;
    bool m_initialized = false;
};

/**
 * Factory class for creating processing step instances
 */
class ProcessingStepFactory {
public:
    enum class StepType {
        FINAL_INITIAL_ALPHA,
        DETECT_HEADS,
        PROCESS_BODY_REGIONS,
        PROCESS_HEAD_REGIONS
    };

    /**
     * Create a processing step instance
     * @param stepType Type of step to create
     * @return Unique pointer to the created step, nullptr on failure
     */
    static std::unique_ptr<ProcessingStep> CreateStep(StepType stepType);

    /**
     * Create a head regions processing step for a specific model size
     * @param headModelSize InsPyReNet model input size (e.g., 320, 640, 1024)
     * @return Unique pointer to the created step, nullptr on failure
     */
    static std::unique_ptr<ProcessingStep> CreateHeadRegionsStep(int headModelSize);

    /**
     * Get step type name for logging
     * @param stepType Step type
     * @return Step type name as string
     */
    static const char* GetStepTypeName(StepType stepType);
};
