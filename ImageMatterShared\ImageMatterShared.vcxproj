<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{86744fcd-247f-4fdf-9829-8425cb131bcf}</ProjectGuid>
    <RootNamespace>ImageMatterShared</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(CUDA_PATH)\include;$(VccLibs)\ffmpeg\include;$(VccLibs)\onnxruntime\include;include;$(VccLibs)\TensorRT\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(CUDA_PATH)\lib\x64;$(VccLibs)\ffmpeg\bin;$(VccLibs)\onnxruntime\lib;$(VccLibs)\TensorRT\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>nvinfer_10.lib;nvonnxparser_10.lib;nvinfer_plugin_10.lib;cudart_static.lib;cuda.lib;cublas.lib;curand.lib;cusparse.lib;cusolver.lib;cufft.lib;avcodec.lib;avformat.lib;avutil.lib;swresample.lib;avfilter.lib;swscale.lib;onnxruntime.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <IgnoreSpecificDefaultLibraries>LIBCMT</IgnoreSpecificDefaultLibraries>
    </Link>
    <PostBuildEvent>
      <Command>	if not exist "$(TargetDir)\ModelsVggHeadDetector" mkdir "$(TargetDir)\ModelsVggHeadDetector"
	if not exist "$(TargetDir)\ModelsMatting" mkdir "$(TargetDir)\ModelsMatting"
	xcopy /E /I /Y /D "$(VccLibs)\onnxruntime\lib\*.dll" "$(TargetDir)" || exit 0
	xcopy /E /I /Y /D "$(VccLibs)\ffmpeg\bin\*.dll" "$(TargetDir)" || exit 0
	xcopy /E /I /Y /D "$(VccLibs)\TensorRT\lib\*.dll" "$(TargetDir)" || exit 0
	xcopy /E /I /Y /D "$(VccLibs)\TensorRT\bin\*.exe" "$(TargetDir)" || exit 0
	xcopy /E /I /Y /D "ModelsVggHeadDetector\*.onnx" "$(TargetDir)\ModelsVggHeadDetector" || exit 0
	xcopy /E /I /Y /D "ModelsMatting\*.*" "$(TargetDir)\ModelsMatting" || exit 0</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>$(CUDA_PATH)\include;$(VccLibs)\ffmpeg\include;$(VccLibs)\onnxruntime\include;include;$(VccLibs)\TensorRT\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(CUDA_PATH)\lib\x64;$(VccLibs)\ffmpeg\bin;$(VccLibs)\onnxruntime\lib;C:\Program Files\NVIDIA GPU Computing Toolkit\TensorRT\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>nvinfer_10.lib;nvonnxparser_10.lib;nvinfer_plugin_10.lib;cudart_static.lib;cuda.lib;cublas.lib;curand.lib;cusparse.lib;cusolver.lib;cufft.lib;avcodec.lib;avformat.lib;avutil.lib;swresample.lib;avfilter.lib;swscale.lib;onnxruntime.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>	if not exist "$(TargetDir)\ModelsVggHeadDetector" mkdir "$(TargetDir)\ModelsVggHeadDetector"
	if not exist "$(TargetDir)\ModelsMatting" mkdir "$(TargetDir)\ModelsMatting"
	xcopy /E /I /Y /D "$(VccLibs)\onnxruntime\lib\*.dll" "$(TargetDir)" || exit 0
	xcopy /E /I /Y /D "$(VccLibs)\ffmpeg\bin\*.dll" "$(TargetDir)" || exit 0
	xcopy /E /I /Y /D "$(VccLibs)\TensorRT\lib\*.dll" "$(TargetDir)" || exit 0
	xcopy /E /I /Y /D "$(VccLibs)\TensorRT\bin\*.exe" "$(TargetDir)" || exit 0
	xcopy /E /I /Y /D "ModelsVggHeadDetector\*.onnx" "$(TargetDir)\ModelsVggHeadDetector" || exit 0
	xcopy /E /I /Y /D "ModelsMatting\*.*" "$(TargetDir)\ModelsMatting" || exit 0</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="include\AlphaVideoExample.h" />
    <ClInclude Include="include\alpha_codec.h" />
    <ClInclude Include="include\AsyncDirectVideoReader.h" />
    <ClInclude Include="include\CudaProResEncoder.h" />
    <ClInclude Include="include\DetectHeadsStep.h" />
    <ClInclude Include="include\DirectVideoReader.h" />
    <ClInclude Include="include\DirectVideoReaderAlpha.h" />
    <ClInclude Include="include\DirectVideoWriter.h" />
    <ClInclude Include="include\DirectVideoWriterAlphaCuda.h" />
    <ClInclude Include="include\FinalInitialAlphaStep.h" />
    <ClInclude Include="src\FrameMetadata.h" />
    <ClInclude Include="include\FrameMetadataStorage.h" />
    <ClInclude Include="include\FrameProcessor.h" />
    <ClInclude Include="include\framework.h" />
    <ClInclude Include="include\HeadDetector.h" />
    <ClInclude Include="include\Helpers.h" />
    <ClInclude Include="include\Helpers_Heads.h" />
    <ClInclude Include="include\ImageMatting.h" />
    <ClInclude Include="include\ImageMattingFactory.h" />
    <ClInclude Include="include\ImageMattingOnnx.h" />
    <ClInclude Include="include\ImageMattingTensorRt.h" />
    <ClInclude Include="include\lodepng.h" />
    <ClInclude Include="include\ProcessBodyRegionsStep.h" />
    <ClInclude Include="include\ProcessHeadRegionsStep.h" />
    <ClInclude Include="include\ProcessingStep.h" />
    <ClInclude Include="include\StringUtils.h" />
    <ClInclude Include="include\VideoBackgroundRemoval.h" />
    <ClInclude Include="include\VideoWriterAlpha.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\alpha_codec.cpp" />
    <ClCompile Include="src\AsyncDirectVideoReader.cpp" />
    <ClCompile Include="src\CudaProResEncoder.cpp" />
    <ClCompile Include="src\DetectHeadsStep.cpp" />
    <ClCompile Include="src\DirectVideoReader.cpp" />
    <ClCompile Include="src\DirectVideoReaderAlpha.cpp" />
    <ClCompile Include="src\DirectVideoWriter.cpp" />
    <ClCompile Include="src\DirectVideoWriterAlphaCuda.cpp" />
    <ClCompile Include="src\FinalInitialAlphaStep.cpp" />
    <ClCompile Include="src\FrameMetadataStorage.cpp" />
    <ClCompile Include="src\FrameProcessor.cpp" />
    <ClCompile Include="src\HeadDetector.cpp" />
    <ClCompile Include="src\Helpers.cpp" />
    <ClCompile Include="src\Helpers_Heads.cpp" />
    <ClCompile Include="src\ImageMatting.cpp" />
    <ClCompile Include="src\ImageMattingFactory.cpp" />
    <ClCompile Include="src\ImageMattingOnnx.cpp" />
    <ClCompile Include="src\ImageMattingTensorRt.cpp" />
    <ClCompile Include="src\lodepng.cpp" />
    <ClCompile Include="src\ProcessBodyRegionsStep.cpp" />
    <ClCompile Include="src\ProcessHeadRegionsStep.cpp" />
    <ClCompile Include="src\ProcessingStep.cpp" />
    <ClCompile Include="src\VideoBackgroundRemoval.cpp" />
    <ClCompile Include="src\VideoWriterAlpha.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="src\alpha_codec.cu" />
    <None Include="include\alpha_codec.cuh" />
    <None Include="src\BackgroundEstimationKernels.cu" />
    <None Include="include\BackgroundEstimationKernels.cuh" />
    <None Include="src\CudaProResKernels.cu" />
    <None Include="src\DownscaleResizingKernels.cu" />
    <None Include="include\DownscaleResizingKernels.cuh" />
    <None Include="src\HeadDetectorKernels.cu" />
    <None Include="include\HeadDetectorKernels.cuh" />
    <None Include="src\Helpers_Kernels.cu" />
    <None Include="include\Helpers_Kernels.cuh" />
    <None Include="src\main_Kernels.cu" />
    <None Include="include\main_Kernels.cuh" />
    <None Include="src\Matting_Kernels.cu" />
    <None Include="include\Matting_Kernels.cuh" />
    <None Include="src\UpscaleResizingKernels.cu" />
    <None Include="include\UpscaleResizingKernels.cuh" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="src\IMPORTANT - Principles.txt" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>